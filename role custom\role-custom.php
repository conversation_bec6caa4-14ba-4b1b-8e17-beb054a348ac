<?php
/**
 * Plugin Name: Role Custom
 * Plugin URI: https://example.com/role-custom
 * Description: WordPress eklentisi - Tutor Instructor rolü için Tutor LMS menülerini kısıtlar, WooCommerce'e tam er<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> menüsünü gizler ve kurs yayınlama izinlerini yönetir.
 * Version: 1.3.0
 * Author: Role Custom Developer
 * Author URI: https://example.com
 * License: GPL v2 or later
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 * Text Domain: role-custom
 * Domain Path: /languages
 * Requires at least: 5.0
 * Tested up to: 6.4
 * Requires PHP: 7.4
 * Network: false
 */

// Doğrudan erişimi engelle
if (!defined('ABSPATH')) {
    exit;
}

// Eklenti sabitleri
define('ROLE_CUSTOM_VERSION', '1.3.0');
define('ROLE_CUSTOM_PLUGIN_FILE', __FILE__);
define('ROLE_CUSTOM_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('ROLE_CUSTOM_PLUGIN_URL', plugin_dir_url(__FILE__));
define('ROLE_CUSTOM_PLUGIN_BASENAME', plugin_basename(__FILE__));

/**
 * Ana Role Custom sınıfı
 */
class Role_Custom {
    
    /**
     * Singleton instance
     */
    private static $instance = null;
    
    /**
     * Tutor Instructor rol adı
     */
    const TUTOR_INSTRUCTOR_ROLE = 'tutor_instructor';
    
    /**
     * Tutor LMS'de izin verilen menüler
     */
    private $allowed_tutor_menus = [
        'tutor',                           // Kurslar (ana sayfa)
        'tutor-students',                  // Öğrenciler
        'tutor_announcements',             // Duyurular
        'question_answer',                 // Q&A
        'tutor_quiz_attempts'              // Sınav Denemeleri
    ];

    /**
     * Tutor LMS öğrenciler sayfası filtreleme durumu
     */
    private $tutor_students_filtering_active = false;

    /**
     * Kullanıcının filtreleme için kullanılacak kursları
     */
    private $user_courses_for_filtering = [];
    
    /**
     * Singleton pattern
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        $this->init_hooks();
    }
    
    /**
     * Hook'ları başlat
     */
    private function init_hooks() {
        // Admin hook'ları
        add_action('admin_menu', [$this, 'restrict_tutor_menus'], 999);
        add_action('admin_menu', [$this, 'customize_tutor_menu_labels'], 1000);
        add_action('admin_menu', [$this, 'restrict_woocommerce_menus'], 999);
        add_action('admin_menu', [$this, 'remove_payments_menu'], 9999);
        add_action('admin_menu', [$this, 'hide_marketing_menu'], 9999);
        add_action('admin_menu', [$this, 'hide_product_taxonomies'], 9999);
        add_action('admin_menu', [$this, 'create_woocommerce_top_level_menus'], 10000);
        add_action('admin_init', [$this, 'setup_woocommerce_capabilities']);
        add_action('admin_init', [$this, 'redirect_dashboard_to_tutor']);
        add_action('admin_notices', [$this, 'admin_notices']);

        // Menü kısıtlama hook'ları
        add_action('admin_head', [$this, 'hide_restricted_menus_css']);
        add_filter('user_has_cap', [$this, 'filter_user_capabilities'], 10, 4);

        // Dil dosyalarını yükle
        add_action('plugins_loaded', [$this, 'load_textdomain']);

        // Eski kalıcı meta veri sistemini temizle
        add_action('admin_init', [$this, 'cleanup_old_superole_instructor_system'], 5);

        // Eski markaları migration et
        add_action('admin_init', [$this, 'migrate_existing_brands'], 6);

        // Eski nitelikleri migration et
        add_action('admin_init', [$this, 'migrate_existing_attributes'], 7);



        // WooCommerce veri filtreleme hook'ları
        add_action('pre_get_posts', [$this, 'filter_woocommerce_data_for_tutor_instructor']);
        add_filter('posts_where', [$this, 'filter_orders_by_user_products'], 10, 2);
        add_filter('woocommerce_orders_table_query_clauses', [$this, 'filter_hpos_orders_by_user_products'], 10, 2);

        // Ürün arama sonuçlarını filtrele
        add_filter('woocommerce_product_pre_search_products', [$this, 'filter_product_search_results'], 10, 6);

        // Sipariş arama sonuçlarını filtrele (CPT orders için)
        add_filter('woocommerce_shop_order_search_results', [$this, 'filter_order_search_results'], 10, 3);

        // Sipariş arama sonuçlarını filtrele (HPOS için)
        add_filter('woocommerce_cot_shop_order_search_results', [$this, 'filter_hpos_order_search_results'], 10, 2);

        // Kupon arama sonuçlarını filtrele
        add_filter('posts_where', [$this, 'filter_coupon_search_results'], 10, 2);

        // WooCommerce admin sayfalarında ek filtreleme
        add_action('load-edit.php', [$this, 'add_woocommerce_admin_filters']);
        add_action('load-admin.php', [$this, 'add_woocommerce_admin_filters']);

        // WooCommerce admin siparişler sayfası için özel hook
        add_action('current_screen', [$this, 'handle_orders_screen']);

        // HPOS için ek hook'lar
        add_action('woocommerce_order_list_table_prepare_items', [$this, 'filter_hpos_order_list']);
        add_filter('woocommerce_order_query_args', [$this, 'filter_order_query_args'], 10, 1);

        // HPOS için en kritik hook - wc_get_orders fonksiyonunu filtrele
        add_filter('woocommerce_orders_table_query_clauses', [$this, 'filter_hpos_orders_by_user_products'], 10, 2);
        add_filter('wc_get_orders_args', [$this, 'filter_wc_get_orders_args'], 10, 1);

        // WooCommerce admin sayfalarında JavaScript ile filtreleme
        add_action('admin_footer', [$this, 'add_order_filtering_js']);

        // WooCommerce ürün row actions'larını düzelt
        add_filter('post_row_actions', [$this, 'fix_product_row_actions'], 999, 2);

        // WooCommerce kupon row actions'larını düzelt
        add_filter('post_row_actions', [$this, 'fix_coupon_row_actions'], 999, 2);

        // Ürün düzenleme yetkilerini güçlendir
        add_filter('map_meta_cap', [$this, 'fix_product_edit_capabilities'], 10, 4);

        // Kupon düzenleme yetkilerini güçlendir
        add_filter('map_meta_cap', [$this, 'fix_coupon_edit_capabilities'], 10, 4);

        // Admin sayfalarında JavaScript ile row actions'ları düzelt
        add_action('admin_footer', [$this, 'add_product_row_actions_js']);

        // Reports menü sayfası için hook'lar
        add_action('admin_menu', [$this, 'add_reports_menu'], 10001);
        add_action('wp_ajax_role_custom_get_sales_data', [$this, 'ajax_get_sales_data']);
        add_action('wp_ajax_role_custom_get_revenue_data', [$this, 'ajax_get_revenue_data']);
        add_action('wp_ajax_role_custom_get_product_performance', [$this, 'ajax_get_product_performance']);
        add_action('wp_ajax_role_custom_get_courses_data', [$this, 'ajax_get_courses_data']);
        add_action('admin_enqueue_scripts', [$this, 'enqueue_reports_scripts']);

        // Role Custom admin menüsü için hook'lar
        add_action('admin_menu', [$this, 'add_role_custom_admin_menu'], 10002);
        add_action('admin_enqueue_scripts', [$this, 'enqueue_admin_scripts']);

        // WordPress ayarlar hook'ları (sadece register_settings ve update callback)
        add_action('admin_init', [$this, 'register_settings']);
        add_action('update_option_role_custom_settings', [$this, 'on_settings_updated'], 10, 2);

        // AJAX hook'ları
        add_action('wp_ajax_role_custom_remove_instructor', [$this, 'ajax_remove_instructor']);
        add_action('wp_ajax_role_custom_debug_user_meta', [$this, 'ajax_debug_user_meta']); // Debug için
        add_action('wp_ajax_role_custom_clean_meta_only', [$this, 'ajax_clean_meta_only']); // Sadece meta temizleme

        // WooCommerce değerlendirmeler filtreleme hook'ları
        add_filter('comments_clauses', [$this, 'filter_product_reviews_by_user_products'], 10, 2);
        add_action('current_screen', [$this, 'handle_reviews_screen']);

        // Tutor LMS öğrenciler sayfası filtreleme hook'ları - sadece tablo filtreleme
        add_action('current_screen', [$this, 'setup_tutor_students_filtering']);
        add_action('admin_footer', [$this, 'add_tutor_students_table_filtering_js']);

        // Tutor LMS kurs yayınlama izni hook'ları - ayarlara göre çalışır
        add_action('init', [$this, 'manage_instructor_course_publishing'], 20);

        // WooCommerce ürün yayınlama izni hook'ları - ayarlara göre çalışır
        add_action('init', [$this, 'manage_instructor_product_publishing'], 21);
        add_filter('woocommerce_new_product_data', [$this, 'filter_new_product_status'], 10, 1);
        add_action('save_post_product', [$this, 'filter_product_save_status'], 10, 3);

        // WooCommerce kupon yayınlama izni hook'ları - ayarlara göre çalışır
        add_action('init', [$this, 'manage_instructor_coupon_publishing'], 22);
        add_filter('wp_insert_post_data', [$this, 'filter_new_coupon_status'], 10, 2);
        add_action('save_post_shop_coupon', [$this, 'filter_coupon_save_status'], 10, 3);

        // WordPress yazı yayınlama izni hook'ları - ayarlara göre çalışır
        add_action('init', [$this, 'manage_instructor_post_publishing'], 25);
        add_filter('wp_insert_post_data', [$this, 'filter_new_post_status'], 10, 2);
        add_action('save_post_post', [$this, 'filter_post_save_status'], 10, 3);

        // WooCommerce marka yayınlama izni hook'ları - ayarlara göre çalışır
        add_action('init', [$this, 'manage_instructor_brand_publishing'], 23);
        add_action('pre_insert_term', [$this, 'filter_new_brand_status'], 10, 2);
        add_action('edit_term', [$this, 'filter_brand_edit_status'], 10, 3);

        // Marka onaylama sistemi hook'ları
        add_filter('manage_edit-product_brand_columns', [$this, 'add_brand_status_column']);
        add_filter('manage_product_brand_custom_column', [$this, 'display_brand_status_column'], 10, 3);
        add_action('wp_ajax_approve_brand', [$this, 'ajax_approve_brand']);
        add_action('wp_ajax_reject_brand', [$this, 'ajax_reject_brand']);
        add_action('admin_footer', [$this, 'add_brand_approval_js']);

        // Pending markaları frontend'de gizle
        add_filter('get_terms', [$this, 'filter_pending_brands'], 10, 4);

        // Markalar sayfasında kullanıcı filtreleme
        add_filter('get_terms', [$this, 'filter_brands_by_user'], 10, 4);

        // WooCommerce nitelik yayınlama izni hook'ları - ayarlara göre çalışır
        add_action('init', [$this, 'manage_instructor_attribute_publishing'], 24);
        add_filter('woocommerce_attribute_added', [$this, 'filter_new_attribute_status'], 10, 2);
        add_action('woocommerce_attribute_updated', [$this, 'filter_attribute_edit_status'], 10, 3);

        // Nitelik onaylama sistemi hook'ları
        add_filter('woocommerce_attribute_taxonomies', [$this, 'filter_pending_attributes_from_frontend']);
        add_action('wp_ajax_approve_attribute', [$this, 'ajax_approve_attribute']);
        add_action('wp_ajax_reject_attribute', [$this, 'ajax_reject_attribute']);
        add_action('admin_footer', [$this, 'add_attribute_approval_js']);

        // Nitelikler sayfasında kullanıcı filtreleme
        add_filter('woocommerce_attribute_taxonomies', [$this, 'filter_attributes_by_user']);

        // Post onaylama sistemi hook'ları
        add_filter('manage_posts_columns', [$this, 'add_post_status_column']);
        add_action('manage_posts_custom_column', [$this, 'display_post_status_column'], 10, 2);
        add_action('wp_ajax_approve_post', [$this, 'ajax_approve_post']);
        add_action('wp_ajax_reject_post', [$this, 'ajax_reject_post']);
        add_action('admin_footer', [$this, 'add_post_approval_js']);

        // Tutor LMS kurs oluşturucu ekranında Author bölümünü gizle
        add_action('admin_head', [$this, 'hide_tutor_course_author_section']);
    }











    /**
     * WordPress ayarlarını kaydet
     */
    public function register_settings() {
        // Sadece admin kullanıcılar için
        if (!current_user_can('manage_options')) {
            return;
        }

        // Ayar grubunu kaydet
        register_setting(
            'role_custom_settings_group',                 // Ayar grubu
            'role_custom_settings',                       // Ayar adı
            [$this, 'sanitize_settings']                 // Sanitize callback
        );

        // Eğitim Ayarları bölümünü ekle
        add_settings_section(
            'role_custom_education_section',              // Section ID
            __('Eğitim Ayarları', 'role-custom'),         // Section başlığı
            [$this, 'education_section_callback'],        // Callback fonksiyonu
            'role-custom-settings'                        // Sayfa slug'ı
        );

        // Eğitmenler kurs yayınlama ayarı
        add_settings_field(
            'instructors_can_publish_courses',            // Field ID
            __('Eğitmenler Kursları Direk Yayınlayabilir ve Düzenleyebilir', 'role-custom'), // Field başlığı
            [$this, 'instructors_can_publish_courses_callback'], // Callback fonksiyonu
            'role-custom-settings',                       // Sayfa slug'ı
            'role_custom_education_section'               // Section ID
        );

        // Eğitmenler yazı yayınlama ayarı
        add_settings_field(
            'instructors_can_publish_posts',              // Field ID
            __('Yazıları Direk Yayınlayabilir ve Düzenleyebilir', 'role-custom'), // Field başlığı
            [$this, 'instructors_can_publish_posts_callback'], // Callback fonksiyonu
            'role-custom-settings',                       // Sayfa slug'ı
            'role_custom_education_section'               // Section ID
        );

        // WooCommerce Yetkileri bölümünü ekle
        add_settings_section(
            'role_custom_woocommerce_section',            // Section ID
            __('WooCommerce Yetkileri', 'role-custom'),   // Section başlığı
            [$this, 'woocommerce_section_callback'],      // Callback fonksiyonu
            'role-custom-settings'                        // Sayfa slug'ı
        );

        // Eğitmenler ürün yayınlama ayarı
        add_settings_field(
            'instructors_can_publish_products',           // Field ID
            __('Ürünleri Yayınlayabilir ve Düzenleyebilir', 'role-custom'), // Field başlığı
            [$this, 'instructors_can_publish_products_callback'], // Callback fonksiyonu
            'role-custom-settings',                       // Sayfa slug'ı
            'role_custom_woocommerce_section'             // Section ID
        );

        // Eğitmenler kupon yayınlama ayarı
        add_settings_field(
            'instructors_can_publish_coupons',            // Field ID
            __('Kuponları Direk Yayın ve Düzenle', 'role-custom'), // Field başlığı
            [$this, 'instructors_can_publish_coupons_callback'], // Callback fonksiyonu
            'role-custom-settings',                       // Sayfa slug'ı
            'role_custom_woocommerce_section'             // Section ID
        );

        // Eğitmenler marka yayınlama ayarı
        add_settings_field(
            'instructors_can_publish_brands',             // Field ID
            __('Markaları Direk Yayın ve Düzenle', 'role-custom'), // Field başlığı
            [$this, 'instructors_can_publish_brands_callback'], // Callback fonksiyonu
            'role-custom-settings',                       // Sayfa slug'ı
            'role_custom_woocommerce_section'             // Section ID
        );

        // Eğitmenler nitelik yayınlama ayarı
        add_settings_field(
            'instructors_can_publish_attributes',         // Field ID
            __('Nitelikleri Direk Yayın ve Düzenle', 'role-custom'), // Field başlığı
            [$this, 'instructors_can_publish_attributes_callback'], // Callback fonksiyonu
            'role-custom-settings',                       // Sayfa slug'ı
            'role_custom_woocommerce_section'             // Section ID
        );
    }

    /**
     * Ayarları sanitize et
     */
    public function sanitize_settings($input) {
        $sanitized = [];

        // Checkbox değerlerini sanitize et
        $sanitized['instructors_can_publish_courses'] = isset($input['instructors_can_publish_courses']) ? 1 : 0;
        $sanitized['instructors_can_publish_posts'] = isset($input['instructors_can_publish_posts']) ? 1 : 0;
        $sanitized['instructors_can_publish_products'] = isset($input['instructors_can_publish_products']) ? 1 : 0;
        $sanitized['instructors_can_publish_coupons'] = isset($input['instructors_can_publish_coupons']) ? 1 : 0;
        $sanitized['instructors_can_publish_brands'] = isset($input['instructors_can_publish_brands']) ? 1 : 0;
        $sanitized['instructors_can_publish_attributes'] = isset($input['instructors_can_publish_attributes']) ? 1 : 0;

        return $sanitized;
    }

    /**
     * Eğitim ayarları bölümü açıklaması
     */
    public function education_section_callback() {
        echo '<p>' . __('Tutor LMS eğitmen yetkilerini yönetin.', 'role-custom') . '</p>';
    }

    /**
     * WooCommerce ayarları bölümü açıklaması
     */
    public function woocommerce_section_callback() {
        echo '<p>' . __('WooCommerce eğitmen yetkilerini yönetin.', 'role-custom') . '</p>';
    }

    /**
     * Eğitmenler kurs yayınlama ayarı callback
     */
    public function instructors_can_publish_courses_callback() {
        $options = get_option('role_custom_settings', []);
        $checked = isset($options['instructors_can_publish_courses']) ? $options['instructors_can_publish_courses'] : 1; // Varsayılan: etkin

        echo '<label>';
        echo '<input type="checkbox" name="role_custom_settings[instructors_can_publish_courses]" value="1" ' . checked(1, $checked, false) . ' />';
        echo ' ' . __('Etkinleştir', 'role-custom');
        echo '</label>';
        echo '<p class="description">';
        echo __('Bu seçenek etkinleştirildiğinde, Tutor Instructor rolündeki kullanıcılar kursları doğrudan yayınlayabilir ve düzenleyebilir. Devre dışı bırakıldığında, kurslar admin onayı bekler.', 'role-custom');
        echo '</p>';
    }

    /**
     * Eğitmenler yazı yayınlama ayarı callback
     */
    public function instructors_can_publish_posts_callback() {
        $options = get_option('role_custom_settings', []);
        $checked = isset($options['instructors_can_publish_posts']) ? $options['instructors_can_publish_posts'] : 1; // Varsayılan: etkin

        echo '<label>';
        echo '<input type="checkbox" name="role_custom_settings[instructors_can_publish_posts]" value="1" ' . checked(1, $checked, false) . ' />';
        echo ' ' . __('Etkinleştir', 'role-custom');
        echo '</label>';
        echo '<p class="description">';
        echo __('Bu seçenek etkinleştirildiğinde, Tutor Instructor rolündeki kullanıcılar yazıları doğrudan yayınlayabilir ve düzenleyebilir. Devre dışı bırakıldığında, yazılar admin onayı bekler.', 'role-custom');
        echo '</p>';
    }

    /**
     * Eğitmenler ürün yayınlama ayarı callback
     */
    public function instructors_can_publish_products_callback() {
        $options = get_option('role_custom_settings', []);
        $checked = isset($options['instructors_can_publish_products']) ? $options['instructors_can_publish_products'] : 1; // Varsayılan: etkin

        echo '<label>';
        echo '<input type="checkbox" name="role_custom_settings[instructors_can_publish_products]" value="1" ' . checked(1, $checked, false) . ' />';
        echo ' ' . __('Etkinleştir', 'role-custom');
        echo '</label>';
        echo '<p class="description">';
        echo __('Bu seçenek etkinleştirildiğinde, Tutor Instructor rolündeki kullanıcılar ürünleri doğrudan yayınlayabilir ve düzenleyebilir. Devre dışı bırakıldığında, ürünler admin onayı bekler.', 'role-custom');
        echo '</p>';
    }

    /**
     * Eğitmenler kupon yayınlama ayarı callback
     */
    public function instructors_can_publish_coupons_callback() {
        $options = get_option('role_custom_settings', []);
        $checked = isset($options['instructors_can_publish_coupons']) ? $options['instructors_can_publish_coupons'] : 1; // Varsayılan: etkin

        echo '<label>';
        echo '<input type="checkbox" name="role_custom_settings[instructors_can_publish_coupons]" value="1" ' . checked(1, $checked, false) . ' />';
        echo ' ' . __('Etkinleştir', 'role-custom');
        echo '</label>';
        echo '<p class="description">';
        echo __('Bu seçenek etkinleştirildiğinde, Tutor Instructor rolündeki kullanıcılar kuponları doğrudan yayınlayabilir ve düzenleyebilir. Devre dışı bırakıldığında, kuponlar admin onayı bekler.', 'role-custom');
        echo '</p>';
    }

    /**
     * Eğitmenler marka yayınlama ayarı callback
     */
    public function instructors_can_publish_brands_callback() {
        $options = get_option('role_custom_settings', []);
        $checked = isset($options['instructors_can_publish_brands']) ? $options['instructors_can_publish_brands'] : 1; // Varsayılan: etkin

        echo '<label>';
        echo '<input type="checkbox" name="role_custom_settings[instructors_can_publish_brands]" value="1" ' . checked(1, $checked, false) . ' />';
        echo ' ' . __('Etkinleştir', 'role-custom');
        echo '</label>';
        echo '<p class="description">';
        echo __('Bu seçenek etkinleştirildiğinde, Tutor Instructor rolündeki kullanıcılar markaları doğrudan oluşturabilir ve düzenleyebilir. Devre dışı bırakıldığında, markalar admin onayı bekler.', 'role-custom');
        echo '</p>';
    }

    /**
     * Eğitmenler nitelik yayınlama ayarı callback
     */
    public function instructors_can_publish_attributes_callback() {
        $options = get_option('role_custom_settings', []);
        $checked = isset($options['instructors_can_publish_attributes']) ? $options['instructors_can_publish_attributes'] : 1; // Varsayılan: etkin

        echo '<label>';
        echo '<input type="checkbox" name="role_custom_settings[instructors_can_publish_attributes]" value="1" ' . checked(1, $checked, false) . ' />';
        echo ' ' . __('Etkinleştir', 'role-custom');
        echo '</label>';
        echo '<p class="description">';
        echo __('Bu seçenek etkinleştirildiğinde, Tutor Instructor rolündeki kullanıcılar ürün niteliklerini doğrudan oluşturabilir ve düzenleyebilir. Devre dışı bırakıldığında, nitelikler admin onayı bekler.', 'role-custom');
        echo '</p>';
    }



    /**
     * Mevcut durumu göster
     */
    public function display_current_status() {
        $options = get_option('role_custom_settings', []);
        $is_enabled = isset($options['instructors_can_publish_courses']) ? $options['instructors_can_publish_courses'] : 1;

        // Kurs yayınlama durum kartı
        echo '<div class="role-custom-status-card">';
        echo '<h4>' . __('🎯 Eğitmen Kurs Yayınlama Durumu', 'role-custom') . '</h4>';

        if ($is_enabled) {
            echo '<p class="status-enabled">✓ ' . __('Etkin - Eğitmenler kursları doğrudan yayınlayabilir', 'role-custom') . '</p>';
            echo '<p>' . __('Kurslar "pending" durumunda değil, doğrudan "publish" durumunda oluşturulur.', 'role-custom') . '</p>';
        } else {
            echo '<p class="status-warning">⚠ ' . __('Pasif - Kurslar admin onayı bekler', 'role-custom') . '</p>';
            echo '<p>' . __('Kurslar "pending" durumunda oluşturulur ve admin onayı gerektirir.', 'role-custom') . '</p>';
        }
        echo '</div>';

        // Ürün yayınlama durum kartı
        $product_options = get_option('role_custom_settings', []);
        $product_enabled = isset($product_options['instructors_can_publish_products']) ? $product_options['instructors_can_publish_products'] : 1;

        echo '<div class="role-custom-status-card">';
        echo '<h4>' . __('🛍️ Eğitmen Ürün Yayınlama Durumu', 'role-custom') . '</h4>';

        if ($product_enabled) {
            echo '<p class="status-enabled">✓ ' . __('Etkin - Eğitmenler ürünleri doğrudan yayınlayabilir', 'role-custom') . '</p>';
            echo '<p>' . __('Ürünler "pending" durumunda değil, doğrudan "publish" durumunda oluşturulur.', 'role-custom') . '</p>';
        } else {
            echo '<p class="status-warning">⚠ ' . __('Pasif - Ürünler admin onayı bekler', 'role-custom') . '</p>';
            echo '<p>' . __('Ürünler "pending" durumunda oluşturulur ve admin onayı gerektirir.', 'role-custom') . '</p>';
        }
        echo '</div>';

        // Kupon yayınlama durum kartı
        $coupon_options = get_option('role_custom_settings', []);
        $coupon_enabled = isset($coupon_options['instructors_can_publish_coupons']) ? $coupon_options['instructors_can_publish_coupons'] : 1;

        echo '<div class="role-custom-status-card">';
        echo '<h4>' . __('🎫 Eğitmen Kupon Yayınlama Durumu', 'role-custom') . '</h4>';

        if ($coupon_enabled) {
            echo '<p class="status-enabled">✓ ' . __('Etkin - Eğitmenler kuponları doğrudan yayınlayabilir', 'role-custom') . '</p>';
            echo '<p>' . __('Kuponlar "pending" durumunda değil, doğrudan "publish" durumunda oluşturulur.', 'role-custom') . '</p>';
        } else {
            echo '<p class="status-warning">⚠ ' . __('Pasif - Kuponlar admin onayı bekler', 'role-custom') . '</p>';
            echo '<p>' . __('Kuponlar "pending" durumunda oluşturulur ve admin onayı gerektirir.', 'role-custom') . '</p>';
        }
        echo '</div>';

        // Marka yayınlama durum kartı
        $brand_options = get_option('role_custom_settings', []);
        $brand_enabled = isset($brand_options['instructors_can_publish_brands']) ? $brand_options['instructors_can_publish_brands'] : 1;

        echo '<div class="role-custom-status-card">';
        echo '<h4>' . __('🏷️ Eğitmen Marka Yönetimi Durumu', 'role-custom') . '</h4>';

        if ($brand_enabled) {
            echo '<p class="status-enabled">✓ ' . __('Etkin - Eğitmenler markaları doğrudan oluşturabilir ve düzenleyebilir', 'role-custom') . '</p>';
            echo '<p>' . __('Markalar doğrudan oluşturulabilir ve düzenlenebilir.', 'role-custom') . '</p>';
        } else {
            echo '<p class="status-warning">⚠ ' . __('Pasif - Marka onay sistemi aktif', 'role-custom') . '</p>';
            echo '<p>' . __('Eğitmenler marka oluşturabilir ancak admin onayı bekler. Onaylanmayan markalar kullanılamaz.', 'role-custom') . '</p>';

            // Pending marka sayısını göster
            $pending_brands = get_terms([
                'taxonomy' => 'product_brand',
                'hide_empty' => false,
                'meta_query' => [
                    [
                        'key' => '_brand_status',
                        'value' => 'pending',
                        'compare' => '='
                    ]
                ]
            ]);

            if (!is_wp_error($pending_brands) && !empty($pending_brands)) {
                $pending_count = count($pending_brands);
                echo '<p><strong>' . sprintf(__('Onay bekleyen marka sayısı: %d', 'role-custom'), $pending_count) . '</strong></p>';
            }
        }
        echo '</div>';

        // Nitelik yayınlama durum kartı
        $attribute_options = get_option('role_custom_settings', []);
        $attribute_enabled = isset($attribute_options['instructors_can_publish_attributes']) ? $attribute_options['instructors_can_publish_attributes'] : 1;

        echo '<div class="role-custom-status-card">';
        echo '<h4>' . __('⚙️ Eğitmen Nitelik Yönetimi Durumu', 'role-custom') . '</h4>';

        if ($attribute_enabled) {
            echo '<p class="status-enabled">✓ ' . __('Etkin - Eğitmenler nitelikleri doğrudan oluşturabilir ve düzenleyebilir', 'role-custom') . '</p>';
            echo '<p>' . __('Ürün nitelikleri doğrudan oluşturulabilir ve düzenlenebilir.', 'role-custom') . '</p>';
        } else {
            echo '<p class="status-warning">⚠ ' . __('Pasif - Nitelik onay sistemi aktif', 'role-custom') . '</p>';
            echo '<p>' . __('Eğitmenler nitelik oluşturabilir ancak admin onayı bekler. Onaylanmayan nitelikler kullanılamaz.', 'role-custom') . '</p>';

            // Pending nitelik sayısını göster
            global $wpdb;
            $pending_count = $wpdb->get_var(
                "SELECT COUNT(*) FROM {$wpdb->options}
                 WHERE option_name LIKE '_attribute_%_status'
                 AND option_value = 'pending'"
            );

            if ($pending_count > 0) {
                echo '<p><strong>' . sprintf(__('Onay bekleyen nitelik sayısı: %d', 'role-custom'), $pending_count) . '</strong></p>';
            }
        }
        echo '</div>';

        // Tutor LMS durumu
        echo '<div class="role-custom-status-card">';
        echo '<h4>' . __('🔧 Tutor LMS Entegrasyonu', 'role-custom') . '</h4>';

        if (class_exists('TUTOR\Tutor')) {
            echo '<p class="status-enabled">✓ ' . __('Tutor LMS eklentisi aktif', 'role-custom') . '</p>';

            $tutor_options = get_option('tutor_option', []);
            $tutor_setting = isset($tutor_options['instructor_can_publish_course']) ? $tutor_options['instructor_can_publish_course'] : 'off';

            echo '<p><strong>' . __('Tutor LMS Ayarı:', 'role-custom') . '</strong> ';
            echo '<code>instructor_can_publish_course = ' . $tutor_setting . '</code></p>';

            if ($tutor_setting === 'on') {
                echo '<p class="status-enabled">✓ ' . __('Tutor LMS ayarı etkin', 'role-custom') . '</p>';
            } else {
                echo '<p class="status-disabled">✗ ' . __('Tutor LMS ayarı pasif', 'role-custom') . '</p>';
            }
        } else {
            echo '<p class="status-disabled">✗ ' . __('Tutor LMS eklentisi aktif değil!', 'role-custom') . '</p>';
            echo '<p>' . __('Bu özelliğin çalışması için Tutor LMS eklentisinin aktif olması gerekir.', 'role-custom') . '</p>';
        }
        echo '</div>';

        // WooCommerce entegrasyonu durumu
        echo '<div class="role-custom-status-card">';
        echo '<h4>' . __('🛒 WooCommerce Entegrasyonu', 'role-custom') . '</h4>';

        if (class_exists('WooCommerce')) {
            echo '<p class="status-enabled">✓ ' . __('WooCommerce eklentisi aktif', 'role-custom') . '</p>';

            $instructor_role = get_role(self::TUTOR_INSTRUCTOR_ROLE);
            if ($instructor_role) {
                if ($instructor_role->has_cap('publish_products')) {
                    echo '<p class="status-enabled">✓ ' . __('publish_products yetkisi mevcut', 'role-custom') . '</p>';
                } else {
                    echo '<p class="status-disabled">✗ ' . __('publish_products yetkisi eksik', 'role-custom') . '</p>';
                }
            }
        } else {
            echo '<p class="status-disabled">✗ ' . __('WooCommerce eklentisi aktif değil!', 'role-custom') . '</p>';
            echo '<p>' . __('Bu özelliğin çalışması için WooCommerce eklentisinin aktif olması gerekir.', 'role-custom') . '</p>';
        }
        echo '</div>';

        // Rol yetkileri durumu
        echo '<div class="role-custom-status-card">';
        echo '<h4>' . __('👥 Tutor Instructor Rol Yetkileri', 'role-custom') . '</h4>';

        $instructor_role = get_role(self::TUTOR_INSTRUCTOR_ROLE);
        if ($instructor_role) {
            echo '<p class="status-enabled">✓ ' . __('tutor_instructor rolü mevcut', 'role-custom') . '</p>';

            if ($instructor_role->has_cap('publish_tutor_courses')) {
                echo '<p class="status-enabled">✓ ' . __('publish_tutor_courses yetkisi mevcut', 'role-custom') . '</p>';
            } else {
                echo '<p class="status-disabled">✗ ' . __('publish_tutor_courses yetkisi eksik', 'role-custom') . '</p>';
            }
        } else {
            echo '<p class="status-disabled">✗ ' . __('tutor_instructor rolü bulunamadı', 'role-custom') . '</p>';
        }
        echo '</div>';

        // Genel durum özeti
        echo '<div class="role-custom-status-card">';
        echo '<h4>' . __('📊 Genel Durum Özeti', 'role-custom') . '</h4>';

        $course_good = $is_enabled &&
                      class_exists('TUTOR\Tutor') &&
                      $instructor_role &&
                      $instructor_role->has_cap('publish_tutor_courses');

        $product_good = $product_enabled &&
                       class_exists('WooCommerce') &&
                       $instructor_role &&
                       $instructor_role->has_cap('publish_products');

        $coupon_good = $coupon_enabled &&
                      class_exists('WooCommerce') &&
                      $instructor_role &&
                      $instructor_role->has_cap('publish_shop_coupons');

        $brand_good = $brand_enabled &&
                     class_exists('WooCommerce') &&
                     $instructor_role &&
                     $instructor_role->has_cap('manage_product_terms');

        $attribute_good = $attribute_enabled &&
                         class_exists('WooCommerce') &&
                         $instructor_role &&
                         $instructor_role->has_cap('manage_product_terms');

        if ($course_good && $product_good && $coupon_good && $brand_good && $attribute_good) {
            echo '<div class="role-custom-success-box">';
            echo '<p><strong>✅ ' . __('Tüm ayarlar doğru yapılandırılmış!', 'role-custom') . '</strong></p>';
            echo '<p>' . __('Eğitmenler artık kursları, ürünleri, kuponları, markaları ve nitelikleri doğrudan yönetebilir.', 'role-custom') . '</p>';
            echo '</div>';
        } else if (!$is_enabled && !$product_enabled && !$coupon_enabled && !$brand_enabled && !$attribute_enabled) {
            echo '<div class="role-custom-warning-box">';
            echo '<p><strong>⚠ ' . __('Tüm yayınlama ayarları pasif durumda', 'role-custom') . '</strong></p>';
            echo '<p>' . __('Tüm içerikler (kurslar, ürünler, kuponlar, markalar, nitelikler) admin onayı bekleyecek. Ayarları etkinleştirerek doğrudan yayınlamayı sağlayabilirsiniz.', 'role-custom') . '</p>';
            echo '</div>';
        } else {
            echo '<div class="role-custom-warning-box">';
            echo '<p><strong>⚠ ' . __('Karışık ayar durumu', 'role-custom') . '</strong></p>';

            $active_features = [];
            $passive_features = [];

            if ($course_good) $active_features[] = __('Kurslar', 'role-custom');
            else $passive_features[] = __('Kurslar', 'role-custom');

            if ($product_good) $active_features[] = __('Ürünler', 'role-custom');
            else $passive_features[] = __('Ürünler', 'role-custom');

            if ($coupon_good) $active_features[] = __('Kuponlar', 'role-custom');
            else $passive_features[] = __('Kuponlar', 'role-custom');

            if ($brand_good) $active_features[] = __('Markalar', 'role-custom');
            else $passive_features[] = __('Markalar', 'role-custom');

            if ($attribute_good) $active_features[] = __('Nitelikler', 'role-custom');
            else $passive_features[] = __('Nitelikler', 'role-custom');

            if (!empty($active_features)) {
                echo '<p><strong>' . __('Etkin:', 'role-custom') . '</strong> ' . implode(', ', $active_features) . '</p>';
            }
            if (!empty($passive_features)) {
                echo '<p><strong>' . __('Pasif:', 'role-custom') . '</strong> ' . implode(', ', $passive_features) . '</p>';
            }
            echo '</div>';
        }
        echo '</div>';
    }

    /**
     * Tutor Instructor rolü için kurs yayınlama iznini yönet (ayarlara göre)
     * Bu sayede instructor'lar ayarlara göre kursları doğrudan yayınlayabilir veya pending durumunda bekler
     */
    public function manage_instructor_course_publishing() {
        // Sadece Tutor LMS aktifse çalıştır
        if (!class_exists('TUTOR\Tutor')) {
            return;
        }

        // Role Custom ayarlarını al
        $role_custom_options = get_option('role_custom_settings', []);
        $is_enabled = isset($role_custom_options['instructors_can_publish_courses']) ? $role_custom_options['instructors_can_publish_courses'] : 1; // Varsayılan: etkin

        // Tutor LMS ayarlarını al
        $tutor_options = get_option('tutor_option', []);

        // Tutor Instructor rolünü al
        $instructor_role = get_role(self::TUTOR_INSTRUCTOR_ROLE);

        if ($is_enabled) {
            // Eğitmenler kursları doğrudan yayınlayabilir
            $tutor_options['instructor_can_publish_course'] = 'on';

            // publish_tutor_courses yetkisini ekle
            if ($instructor_role && !$instructor_role->has_cap('publish_tutor_courses')) {
                $instructor_role->add_cap('publish_tutor_courses');
            }
        } else {
            // Eğitmenler kursları admin onayı bekler
            $tutor_options['instructor_can_publish_course'] = 'off';

            // publish_tutor_courses yetkisini kaldır
            if ($instructor_role && $instructor_role->has_cap('publish_tutor_courses')) {
                $instructor_role->remove_cap('publish_tutor_courses');
            }
        }

        // Tutor LMS ayarlarını güncelle
        update_option('tutor_option', $tutor_options);
    }

    /**
     * WooCommerce ürün yayınlama iznini yönet (ayarlara göre)
     * Bu sayede instructor'lar ayarlara göre ürünleri doğrudan yayınlayabilir veya pending durumunda bekler
     */
    public function manage_instructor_product_publishing() {
        // Sadece WooCommerce aktifse çalıştır
        if (!class_exists('WooCommerce')) {
            return;
        }

        // Role Custom ayarlarını al
        $role_custom_options = get_option('role_custom_settings', []);
        $is_enabled = isset($role_custom_options['instructors_can_publish_products']) ? $role_custom_options['instructors_can_publish_products'] : 1; // Varsayılan: etkin

        // Tutor Instructor rolünü al
        $instructor_role = get_role(self::TUTOR_INSTRUCTOR_ROLE);

        if ($is_enabled) {
            // Eğitmenler ürünleri doğrudan yayınlayabilir
            if ($instructor_role && !$instructor_role->has_cap('publish_products')) {
                $instructor_role->add_cap('publish_products');
            }
        } else {
            // Eğitmenler ürünleri admin onayı bekler
            if ($instructor_role && $instructor_role->has_cap('publish_products')) {
                $instructor_role->remove_cap('publish_products');
            }
        }
    }

    /**
     * Yeni ürün oluşturulurken durumunu filtrele
     */
    public function filter_new_product_status($product_data) {
        // Sadece Tutor Instructor rolündeki kullanıcılar için
        if (!$this->is_current_user_tutor_instructor()) {
            return $product_data;
        }

        // Role Custom ayarlarını al
        $role_custom_options = get_option('role_custom_settings', []);
        $is_enabled = isset($role_custom_options['instructors_can_publish_products']) ? $role_custom_options['instructors_can_publish_products'] : 1;

        // Eğer ayar pasifse ve ürün publish durumundaysa, pending yap
        if (!$is_enabled && isset($product_data['post_status']) && $product_data['post_status'] === 'publish') {
            $product_data['post_status'] = 'pending';
        }

        return $product_data;
    }

    /**
     * Ürün kaydedilirken durumunu filtrele
     */
    public function filter_product_save_status($post_id, $post, $update) {
        // Sadece Tutor Instructor rolündeki kullanıcılar için
        if (!$this->is_current_user_tutor_instructor()) {
            return;
        }

        // Sadece ürün güncellemelerinde çalış
        if (!$update || $post->post_type !== 'product') {
            return;
        }

        // Role Custom ayarlarını al
        $role_custom_options = get_option('role_custom_settings', []);
        $is_enabled = isset($role_custom_options['instructors_can_publish_products']) ? $role_custom_options['instructors_can_publish_products'] : 1;

        // Eğer ayar pasifse ve ürün publish durumundaysa, pending yap
        if (!$is_enabled && $post->post_status === 'publish') {
            // Post durumunu güncelle
            wp_update_post([
                'ID' => $post_id,
                'post_status' => 'pending'
            ]);
        }
    }

    /**
     * WooCommerce kupon yayınlama iznini yönet (ayarlara göre)
     * Bu sayede instructor'lar ayarlara göre kuponları doğrudan yayınlayabilir veya pending durumunda bekler
     */
    public function manage_instructor_coupon_publishing() {
        // Sadece WooCommerce aktifse çalıştır
        if (!class_exists('WooCommerce')) {
            return;
        }

        // Role Custom ayarlarını al
        $role_custom_options = get_option('role_custom_settings', []);
        $is_enabled = isset($role_custom_options['instructors_can_publish_coupons']) ? $role_custom_options['instructors_can_publish_coupons'] : 1; // Varsayılan: etkin

        // Tutor Instructor rolünü al
        $instructor_role = get_role(self::TUTOR_INSTRUCTOR_ROLE);

        if ($is_enabled) {
            // Eğitmenler kuponları doğrudan yayınlayabilir
            if ($instructor_role && !$instructor_role->has_cap('publish_shop_coupons')) {
                $instructor_role->add_cap('publish_shop_coupons');
            }
        } else {
            // Eğitmenler kuponları admin onayı bekler
            if ($instructor_role && $instructor_role->has_cap('publish_shop_coupons')) {
                $instructor_role->remove_cap('publish_shop_coupons');
            }
        }
    }

    /**
     * Yeni kupon oluşturulurken durumunu filtrele
     */
    public function filter_new_coupon_status($data, $postarr) {
        // Sadece Tutor Instructor rolündeki kullanıcılar için
        if (!$this->is_current_user_tutor_instructor()) {
            return $data;
        }

        // Sadece kupon post type'ı için
        if (!isset($data['post_type']) || $data['post_type'] !== 'shop_coupon') {
            return $data;
        }

        // Role Custom ayarlarını al
        $role_custom_options = get_option('role_custom_settings', []);
        $is_enabled = isset($role_custom_options['instructors_can_publish_coupons']) ? $role_custom_options['instructors_can_publish_coupons'] : 1;

        // Eğer ayar pasifse ve kupon publish durumundaysa, pending yap
        if (!$is_enabled && isset($data['post_status']) && $data['post_status'] === 'publish') {
            $data['post_status'] = 'pending';
        }

        return $data;
    }

    /**
     * Kupon kaydedilirken durumunu filtrele
     */
    public function filter_coupon_save_status($post_id, $post, $update) {
        // Sadece Tutor Instructor rolündeki kullanıcılar için
        if (!$this->is_current_user_tutor_instructor()) {
            return;
        }

        // Sadece kupon güncellemelerinde çalış
        if (!$update || $post->post_type !== 'shop_coupon') {
            return;
        }

        // Role Custom ayarlarını al
        $role_custom_options = get_option('role_custom_settings', []);
        $is_enabled = isset($role_custom_options['instructors_can_publish_coupons']) ? $role_custom_options['instructors_can_publish_coupons'] : 1;

        // Eğer ayar pasifse ve kupon publish durumundaysa, pending yap
        if (!$is_enabled && $post->post_status === 'publish') {
            // Post durumunu güncelle
            wp_update_post([
                'ID' => $post_id,
                'post_status' => 'pending'
            ]);
        }
    }

    /**
     * WordPress yazı yayınlama iznini yönet (ayarlara göre)
     * Bu sayede instructor'lar ayarlara göre yazıları doğrudan yayınlayabilir veya admin onayı bekler
     */
    public function manage_instructor_post_publishing() {
        // Role Custom ayarlarını al
        $role_custom_options = get_option('role_custom_settings', []);
        $is_enabled = isset($role_custom_options['instructors_can_publish_posts']) ? $role_custom_options['instructors_can_publish_posts'] : 1; // Varsayılan: etkin

        // Tutor Instructor rolünü al
        $instructor_role = get_role(self::TUTOR_INSTRUCTOR_ROLE);

        if ($is_enabled) {
            // Eğitmenler yazıları doğrudan yayınlayabilir
            if ($instructor_role && !$instructor_role->has_cap('publish_posts')) {
                $instructor_role->add_cap('publish_posts');
            }
        } else {
            // Eğitmenler yazıları admin onayı bekler
            if ($instructor_role && $instructor_role->has_cap('publish_posts')) {
                $instructor_role->remove_cap('publish_posts');
            }
        }
    }

    /**
     * Yeni oluşturulan yazının durumunu filtrele
     */
    public function filter_new_post_status($data, $postarr) {
        // Sadece Tutor Instructor rolündeki kullanıcılar için
        if (!$this->is_current_user_tutor_instructor()) {
            return $data;
        }

        // Sadece post post type'ı için
        if (!isset($data['post_type']) || $data['post_type'] !== 'post') {
            return $data;
        }

        // Role Custom ayarlarını al
        $role_custom_options = get_option('role_custom_settings', []);
        $is_enabled = isset($role_custom_options['instructors_can_publish_posts']) ? $role_custom_options['instructors_can_publish_posts'] : 1;

        // Eğer ayar pasifse ve yazı publish durumundaysa, pending yap
        if (!$is_enabled && isset($data['post_status']) && $data['post_status'] === 'publish') {
            $data['post_status'] = 'pending';
        }

        return $data;
    }

    /**
     * Yazı kaydedilirken durumunu filtrele
     */
    public function filter_post_save_status($post_id, $post, $update) {
        // Sadece Tutor Instructor rolündeki kullanıcılar için
        if (!$this->is_current_user_tutor_instructor()) {
            return;
        }

        // Sadece yazı güncellemelerinde çalış
        if (!$update || $post->post_type !== 'post') {
            return;
        }

        // Role Custom ayarlarını al
        $role_custom_options = get_option('role_custom_settings', []);
        $is_enabled = isset($role_custom_options['instructors_can_publish_posts']) ? $role_custom_options['instructors_can_publish_posts'] : 1;

        // Eğer ayar pasifse ve yazı publish durumundaysa, pending yap
        if (!$is_enabled && $post->post_status === 'publish') {
            // Post durumunu güncelle
            wp_update_post([
                'ID' => $post_id,
                'post_status' => 'pending'
            ]);
        }
    }

    /**
     * WooCommerce marka yayınlama iznini yönet (ayarlara göre)
     * Bu sayede instructor'lar ayarlara göre markaları doğrudan oluşturabilir veya admin onayı bekler
     */
    public function manage_instructor_brand_publishing() {
        // Sadece WooCommerce aktifse çalıştır
        if (!class_exists('WooCommerce')) {
            return;
        }

        // Role Custom ayarlarını al
        $role_custom_options = get_option('role_custom_settings', []);
        $is_enabled = isset($role_custom_options['instructors_can_publish_brands']) ? $role_custom_options['instructors_can_publish_brands'] : 1; // Varsayılan: etkin

        // Tutor Instructor rolünü al
        $instructor_role = get_role(self::TUTOR_INSTRUCTOR_ROLE);

        if ($is_enabled) {
            // Eğitmenler markaları doğrudan oluşturabilir
            if ($instructor_role && !$instructor_role->has_cap('manage_product_terms')) {
                $instructor_role->add_cap('manage_product_terms');
                $instructor_role->add_cap('edit_product_terms');
                $instructor_role->add_cap('assign_product_terms');
            }
        } else {
            // Eğitmenler markaları admin onayı bekler - yetkiler kaldırılmaz çünkü mevcut markaları kullanabilmeleri gerekir
            // Sadece yeni marka oluşturma ve düzenleme kısıtlanır
        }
    }

    /**
     * Yeni marka oluşturulurken durumunu filtrele
     */
    public function filter_new_brand_status($term, $taxonomy) {
        // Sadece product_brand taxonomy'si için
        if ($taxonomy !== 'product_brand') {
            return $term;
        }

        // Tüm kullanıcılar için created_term hook'unu ekle (created_by meta'sı için)
        add_action('created_term', [$this, 'mark_brand_as_pending'], 10, 3);

        return $term;
    }

    /**
     * Yeni oluşturulan markayı pending durumuna işaretle
     */
    public function mark_brand_as_pending($term_id, $tt_id, $taxonomy) {
        // Sadece product_brand taxonomy'si için
        if ($taxonomy !== 'product_brand') {
            return;
        }

        // Tüm kullanıcılar için created_by meta'sını ekle
        $current_user_id = get_current_user_id();
        if ($current_user_id) {
            update_term_meta($term_id, '_brand_created_by', $current_user_id);
            update_term_meta($term_id, '_brand_created_date', current_time('mysql'));
        }

        // Sadece Tutor Instructor rolündeki kullanıcılar için pending kontrolü
        if (!$this->is_current_user_tutor_instructor()) {
            // Hook'u kaldır (sadece bir kez çalışsın)
            remove_action('created_term', [$this, 'mark_brand_as_pending'], 10);
            return;
        }

        // Role Custom ayarlarını al
        $role_custom_options = get_option('role_custom_settings', []);
        $is_enabled = isset($role_custom_options['instructors_can_publish_brands']) ? $role_custom_options['instructors_can_publish_brands'] : 1;

        // Eğer ayar pasifse, markayı pending olarak işaretle
        if (!$is_enabled) {
            update_term_meta($term_id, '_brand_status', 'pending');
        }

        // Hook'u kaldır (sadece bir kez çalışsın)
        remove_action('created_term', [$this, 'mark_brand_as_pending'], 10);
    }

    /**
     * Marka düzenlenirken durumunu kontrol et
     */
    public function filter_brand_edit_status($term_id, $tt_id, $taxonomy) {
        // Sadece Tutor Instructor rolündeki kullanıcılar için
        if (!$this->is_current_user_tutor_instructor()) {
            return;
        }

        // Sadece product_brand taxonomy'si için
        if ($taxonomy !== 'product_brand') {
            return;
        }

        // Role Custom ayarlarını al
        $role_custom_options = get_option('role_custom_settings', []);
        $is_enabled = isset($role_custom_options['instructors_can_publish_brands']) ? $role_custom_options['instructors_can_publish_brands'] : 1;

        // Eğer ayar pasifse, marka düzenleme sonrası pending durumuna işaretle
        if (!$is_enabled) {
            // Mevcut durumu kontrol et
            $current_status = get_term_meta($term_id, '_brand_status', true);

            // Eğer zaten pending değilse, pending yap
            if ($current_status !== 'pending') {
                update_term_meta($term_id, '_brand_status', 'pending');
                update_term_meta($term_id, '_brand_modified_by', get_current_user_id());
                update_term_meta($term_id, '_brand_modified_date', current_time('mysql'));
            }
        }
    }

    /**
     * Marka listesine durum kolonu ekle
     */
    public function add_brand_status_column($columns) {
        // Sadece admin kullanıcılar için
        if (!current_user_can('manage_options')) {
            return $columns;
        }

        $columns['brand_status'] = __('Durum', 'role-custom');
        return $columns;
    }

    /**
     * Marka durum kolonunu göster
     */
    public function display_brand_status_column($content, $column_name, $term_id) {
        if ($column_name !== 'brand_status') {
            return $content;
        }

        // Sadece admin kullanıcılar için
        if (!current_user_can('manage_options')) {
            return $content;
        }

        $brand_status = get_term_meta($term_id, '_brand_status', true);
        $created_by = get_term_meta($term_id, '_brand_created_by', true);
        $created_date = get_term_meta($term_id, '_brand_created_date', true);

        if ($brand_status === 'pending') {
            $user_info = '';
            if ($created_by) {
                $user = get_user_by('id', $created_by);
                if ($user) {
                    $user_info = ' - ' . $user->display_name;
                }
            }

            $date_info = '';
            if ($created_date) {
                $date_info = '<br><small>' . date('d.m.Y H:i', strtotime($created_date)) . '</small>';
            }

            $content = '<span style="color: #d63638; font-weight: bold;">⏳ ' . __('Onay Bekliyor', 'role-custom') . '</span>' . $user_info . $date_info;
            $content .= '<br><div style="margin-top: 5px;">';
            $content .= '<button type="button" class="button button-small" onclick="approveBrand(' . $term_id . ')" style="background: #00a32a; color: white; border: none; margin-right: 5px;">✓ ' . __('Onayla', 'role-custom') . '</button>';
            $content .= '<button type="button" class="button button-small" onclick="rejectBrand(' . $term_id . ')" style="background: #d63638; color: white; border: none;">✗ ' . __('Reddet', 'role-custom') . '</button>';
            $content .= '</div>';
        } else {
            $content = '<span style="color: #00a32a; font-weight: bold;">✓ ' . __('Onaylanmış', 'role-custom') . '</span>';
        }

        return $content;
    }

    /**
     * AJAX: Markayı onayla
     */
    public function ajax_approve_brand() {
        // Güvenlik kontrolü
        if (!current_user_can('manage_options')) {
            wp_die(__('Yetkiniz yok.', 'role-custom'));
        }

        $term_id = intval($_POST['term_id']);
        if (!$term_id) {
            wp_die(__('Geçersiz marka ID.', 'role-custom'));
        }

        // Marka durumunu onayla
        update_term_meta($term_id, '_brand_status', 'approved');
        update_term_meta($term_id, '_brand_approved_by', get_current_user_id());
        update_term_meta($term_id, '_brand_approved_date', current_time('mysql'));

        wp_send_json_success([
            'message' => __('Marka başarıyla onaylandı.', 'role-custom')
        ]);
    }

    /**
     * AJAX: Markayı reddet
     */
    public function ajax_reject_brand() {
        // Güvenlik kontrolü
        if (!current_user_can('manage_options')) {
            wp_die(__('Yetkiniz yok.', 'role-custom'));
        }

        $term_id = intval($_POST['term_id']);
        if (!$term_id) {
            wp_die(__('Geçersiz marka ID.', 'role-custom'));
        }

        // Markayı sil
        $result = wp_delete_term($term_id, 'product_brand');

        if (is_wp_error($result)) {
            wp_send_json_error([
                'message' => __('Marka silinirken hata oluştu.', 'role-custom')
            ]);
        } else {
            wp_send_json_success([
                'message' => __('Marka başarıyla reddedildi ve silindi.', 'role-custom')
            ]);
        }
    }

    /**
     * Pending markaları frontend'de gizle
     */
    public function filter_pending_brands($terms, $taxonomies, $args, $term_query) {
        // Sadece product_brand taxonomy'si için
        if (!in_array('product_brand', $taxonomies)) {
            return $terms;
        }

        // Admin panelinde pending markaları göster (admin onaylayabilsin)
        if (is_admin() && current_user_can('manage_options')) {
            return $terms;
        }

        // Frontend'de ve admin olmayan kullanıcılar için pending markaları filtrele
        if (is_array($terms)) {
            $filtered_terms = [];
            foreach ($terms as $term) {
                $brand_status = get_term_meta($term->term_id, '_brand_status', true);

                // Pending olmayan markaları dahil et
                if ($brand_status !== 'pending') {
                    $filtered_terms[] = $term;
                }
            }
            return $filtered_terms;
        }

        return $terms;
    }

    /**
     * Markaları kullanıcıya göre filtrele - sadece kendi oluşturduğu markaları göster
     */
    public function filter_brands_by_user($terms, $taxonomies, $args, $term_query) {
        // Sadece admin panelinde çalışsın
        if (!is_admin()) {
            return $terms;
        }

        // Sadece product_brand taxonomy'si için
        if (!in_array('product_brand', $taxonomies)) {
            return $terms;
        }

        // Sadece tutor instructor rolündeki kullanıcılar için
        if (!$this->is_current_user_tutor_instructor()) {
            return $terms;
        }

        // Admin kullanıcıları için filtreleme yapma
        if (current_user_can('manage_options')) {
            return $terms;
        }

        // Markalar sayfası kontrolü
        global $pagenow;
        if ($pagenow !== 'edit-tags.php' || !isset($_GET['taxonomy']) || $_GET['taxonomy'] !== 'product_brand') {
            return $terms;
        }

        $current_user_id = get_current_user_id();

        // Terms array ise filtrele
        if (is_array($terms)) {
            $filtered_terms = [];
            foreach ($terms as $term) {
                $brand_created_by = get_term_meta($term->term_id, '_brand_created_by', true);

                // Eğer marka bu kullanıcı tarafından oluşturulduysa dahil et
                // Veya eski markalar için (created_by meta'sı yoksa) tüm markaları göster
                if (empty($brand_created_by) || $brand_created_by == $current_user_id) {
                    $filtered_terms[] = $term;
                }
            }
            return $filtered_terms;
        }

        return $terms;
    }

    /**
     * Mevcut markaları migration et - created_by meta'sını ekle
     */
    public function migrate_existing_brands() {
        // Migration'ın daha önce yapılıp yapılmadığını kontrol et
        $migration_done = get_option('role_custom_brands_migration_done', false);
        if ($migration_done) {
            return;
        }

        // Tüm markaları al
        $brands = get_terms([
            'taxonomy' => 'product_brand',
            'hide_empty' => false,
            'meta_query' => [
                [
                    'key' => '_brand_created_by',
                    'compare' => 'NOT EXISTS'
                ]
            ]
        ]);

        if (!empty($brands) && !is_wp_error($brands)) {
            foreach ($brands as $brand) {
                // Eski markalar için admin kullanıcısını created_by olarak ata
                // Veya ilk admin kullanıcısını bul
                $admin_users = get_users(['role' => 'administrator', 'number' => 1]);
                if (!empty($admin_users)) {
                    $admin_id = $admin_users[0]->ID;
                    update_term_meta($brand->term_id, '_brand_created_by', $admin_id);
                    update_term_meta($brand->term_id, '_brand_created_date', current_time('mysql'));
                }
            }
        }

        // Migration'ın tamamlandığını işaretle
        update_option('role_custom_brands_migration_done', true);
    }

    /**
     * Mevcut nitelikleri migration et - created_by meta'sını ekle
     */
    public function migrate_existing_attributes() {
        // Migration'ın daha önce yapılıp yapılmadığını kontrol et
        $migration_done = get_option('role_custom_attributes_migration_done', false);
        if ($migration_done) {
            return;
        }

        // Tüm nitelikleri al
        $attributes = wc_get_attribute_taxonomies();

        if (!empty($attributes)) {
            foreach ($attributes as $attribute) {
                // Mevcut created_by meta'sını kontrol et
                $existing_created_by = get_option('_attribute_' . $attribute->attribute_id . '_created_by', '');

                if (empty($existing_created_by)) {
                    // Eski nitelikler için admin kullanıcısını created_by olarak ata
                    $admin_users = get_users(['role' => 'administrator', 'number' => 1]);
                    if (!empty($admin_users)) {
                        $admin_id = $admin_users[0]->ID;
                        update_option('_attribute_' . $attribute->attribute_id . '_created_by', $admin_id);
                        update_option('_attribute_' . $attribute->attribute_id . '_created_date', current_time('mysql'));
                    }
                }
            }
        }

        // Migration'ın tamamlandığını işaretle
        update_option('role_custom_attributes_migration_done', true);
    }

    /**
     * Marka onaylama JavaScript'i ekle
     */
    public function add_brand_approval_js() {
        $screen = get_current_screen();

        // Sadece marka düzenleme sayfasında ve admin kullanıcılar için
        if (!$screen || $screen->id !== 'edit-product_brand' || !current_user_can('manage_options')) {
            return;
        }

        ?>
        <script type="text/javascript">
        function approveBrand(termId) {
            if (!confirm('<?php echo esc_js(__('Bu markayı onaylamak istediğinizden emin misiniz?', 'role-custom')); ?>')) {
                return;
            }

            jQuery.post(ajaxurl, {
                action: 'approve_brand',
                term_id: termId
            }, function(response) {
                if (response.success) {
                    alert(response.data.message);
                    location.reload();
                } else {
                    alert('<?php echo esc_js(__('Hata oluştu.', 'role-custom')); ?>');
                }
            });
        }

        function rejectBrand(termId) {
            if (!confirm('<?php echo esc_js(__('Bu markayı reddetmek ve silmek istediğinizden emin misiniz? Bu işlem geri alınamaz.', 'role-custom')); ?>')) {
                return;
            }

            jQuery.post(ajaxurl, {
                action: 'reject_brand',
                term_id: termId
            }, function(response) {
                if (response.success) {
                    alert(response.data.message);
                    location.reload();
                } else {
                    alert('<?php echo esc_js(__('Hata oluştu.', 'role-custom')); ?>');
                }
            });
        }
        </script>
        <?php
    }

    /**
     * WooCommerce nitelik yayınlama iznini yönet (ayarlara göre)
     * Bu sayede instructor'lar ayarlara göre nitelikleri doğrudan oluşturabilir veya admin onayı bekler
     */
    public function manage_instructor_attribute_publishing() {
        // Sadece WooCommerce aktifse çalıştır
        if (!class_exists('WooCommerce')) {
            return;
        }

        // Role Custom ayarlarını al
        $role_custom_options = get_option('role_custom_settings', []);
        $is_enabled = isset($role_custom_options['instructors_can_publish_attributes']) ? $role_custom_options['instructors_can_publish_attributes'] : 1; // Varsayılan: etkin

        // Tutor Instructor rolünü al
        $instructor_role = get_role(self::TUTOR_INSTRUCTOR_ROLE);

        if ($is_enabled) {
            // Eğitmenler nitelikleri doğrudan oluşturabilir
            if ($instructor_role && !$instructor_role->has_cap('manage_product_terms')) {
                $instructor_role->add_cap('manage_product_terms');
                $instructor_role->add_cap('edit_product_terms');
            }
        } else {
            // Eğitmenler nitelikleri admin onayı bekler - yetkiler kaldırılmaz çünkü mevcut nitelikleri kullanabilmeleri gerekir
            // Sadece yeni nitelik oluşturma ve düzenleme kısıtlanır
        }
    }

    /**
     * Yeni nitelik oluşturulurken durumunu filtrele
     */
    public function filter_new_attribute_status($attribute_id, $attribute_data) {
        // Tüm kullanıcılar için created_by meta'sını ekle
        $current_user_id = get_current_user_id();
        if ($current_user_id) {
            update_option('_attribute_' . $attribute_id . '_created_by', $current_user_id);
            update_option('_attribute_' . $attribute_id . '_created_date', current_time('mysql'));
        }

        // Sadece Tutor Instructor rolündeki kullanıcılar için pending kontrolü
        if (!$this->is_current_user_tutor_instructor()) {
            return;
        }

        // Role Custom ayarlarını al
        $role_custom_options = get_option('role_custom_settings', []);
        $is_enabled = isset($role_custom_options['instructors_can_publish_attributes']) ? $role_custom_options['instructors_can_publish_attributes'] : 1;

        // Eğer ayar pasifse, niteliği pending olarak işaretle
        if (!$is_enabled) {
            // Meta bilgileri ekle
            update_option('_attribute_' . $attribute_id . '_status', 'pending');
            update_option('_attribute_' . $attribute_id . '_original_data', $attribute_data);

            // Cache'i temizle ki filtreleme çalışsın
            delete_transient('wc_attribute_taxonomies');
        }
    }

    /**
     * Nitelik düzenlenirken durumunu kontrol et
     */
    public function filter_attribute_edit_status($attribute_id, $attribute_data, $old_attribute_data) {
        // Sadece Tutor Instructor rolündeki kullanıcılar için
        if (!$this->is_current_user_tutor_instructor()) {
            return;
        }

        // Role Custom ayarlarını al
        $role_custom_options = get_option('role_custom_settings', []);
        $is_enabled = isset($role_custom_options['instructors_can_publish_attributes']) ? $role_custom_options['instructors_can_publish_attributes'] : 1;

        // Eğer ayar pasifse, nitelik düzenleme sonrası pending durumuna işaretle
        if (!$is_enabled) {
            // Mevcut durumu kontrol et
            $current_status = get_option('_attribute_' . $attribute_id . '_status', '');

            // Eğer zaten pending değilse, pending yap
            if ($current_status !== 'pending') {
                update_option('_attribute_' . $attribute_id . '_status', 'pending');
                update_option('_attribute_' . $attribute_id . '_modified_by', get_current_user_id());
                update_option('_attribute_' . $attribute_id . '_modified_date', current_time('mysql'));

                // Cache'i temizle ki filtreleme çalışsın
                delete_transient('wc_attribute_taxonomies');
            }
        }
    }

    /**
     * Ayarlar güncellendiğinde çalışır
     */
    public function on_settings_updated($old_value, $new_value) {
        // Kurs yayınlama ayarlarını yeniden uygula
        $this->manage_instructor_course_publishing();

        // Ürün yayınlama ayarlarını yeniden uygula
        $this->manage_instructor_product_publishing();

        // Kupon yayınlama ayarlarını yeniden uygula
        $this->manage_instructor_coupon_publishing();

        // Yazı yayınlama ayarlarını yeniden uygula
        $this->manage_instructor_post_publishing();

        // Marka yayınlama ayarlarını yeniden uygula
        $this->manage_instructor_brand_publishing();

        // Nitelik yayınlama ayarlarını yeniden uygula
        $this->manage_instructor_attribute_publishing();
    }

    /**
     * Pending nitelikleri ana tablodan gizle ve ayrı tabloda göster
     */
    public function filter_pending_attributes_from_frontend($attributes) {
        // Frontend'de ve ürün düzenleme sayfalarında pending nitelikleri filtrele
        if (is_array($attributes)) {
            $filtered_attributes = [];
            foreach ($attributes as $attribute) {
                $attribute_status = get_option('_attribute_' . $attribute->attribute_id . '_status', '');

                // Pending olmayan nitelikleri dahil et (ana tabloda göster)
                if ($attribute_status !== 'pending') {
                    $filtered_attributes[] = $attribute;
                }
            }
            return $filtered_attributes;
        }

        return $attributes;
    }

    /**
     * Nitelikleri kullanıcıya göre filtrele - sadece kendi oluşturduğu nitelikleri göster
     */
    public function filter_attributes_by_user($attributes) {
        // Sadece admin panelinde çalışsın
        if (!is_admin()) {
            return $attributes;
        }

        // Sadece tutor instructor rolündeki kullanıcılar için
        if (!$this->is_current_user_tutor_instructor()) {
            return $attributes;
        }

        // Admin kullanıcıları için filtreleme yapma
        if (current_user_can('manage_options')) {
            return $attributes;
        }

        // Nitelikler sayfası kontrolü
        global $pagenow;
        if ($pagenow !== 'edit.php' || !isset($_GET['page']) || $_GET['page'] !== 'product_attributes') {
            return $attributes;
        }

        $current_user_id = get_current_user_id();

        // Attributes array ise filtrele
        if (is_array($attributes)) {
            $filtered_attributes = [];
            foreach ($attributes as $attribute) {
                $attribute_created_by = get_option('_attribute_' . $attribute->attribute_id . '_created_by', '');

                // Eğer nitelik bu kullanıcı tarafından oluşturulduysa dahil et
                // Veya eski nitelikler için (created_by meta'sı yoksa) tüm nitelikleri göster
                if (empty($attribute_created_by) || $attribute_created_by == $current_user_id) {
                    $filtered_attributes[] = $attribute;
                }
            }
            return $filtered_attributes;
        }

        return $attributes;
    }

    /**
     * Postlar tablosuna durum kolonu ekle
     */
    public function add_post_status_column($columns) {
        // Sadece admin kullanıcılar için
        if (!current_user_can('manage_options')) {
            return $columns;
        }

        // Sadece post sayfasında
        global $typenow;
        if ($typenow !== 'post') {
            return $columns;
        }

        // Durum kolonu ekle
        $new_columns = [];
        foreach ($columns as $key => $value) {
            $new_columns[$key] = $value;
            if ($key === 'title') {
                $new_columns['post_approval_status'] = __('Onay Durumu', 'role-custom');
            }
        }

        return $new_columns;
    }

    /**
     * Post durum kolonunu göster
     */
    public function display_post_status_column($column, $post_id) {
        if ($column !== 'post_approval_status') {
            return;
        }

        // Sadece admin kullanıcılar için
        if (!current_user_can('manage_options')) {
            return;
        }

        $post = get_post($post_id);
        if (!$post) {
            return;
        }

        // Post yazarının bilgilerini al
        $author = get_user_by('ID', $post->post_author);
        $author_name = $author ? $author->display_name : __('Bilinmeyen', 'role-custom');

        // Tarih bilgisi
        $date_info = '<br><small style="color: #666;">' .
                    sprintf(__('Oluşturulma: %s', 'role-custom'),
                    get_the_date('d.m.Y H:i', $post_id)) . '</small>';

        // Post durumuna göre içerik oluştur
        if ($post->post_status === 'pending') {
            $user_info = '<br><small style="color: #666;">' .
                        sprintf(__('Yazar: %s', 'role-custom'), $author_name) . '</small>';

            $content = '<span style="color: #d63638; font-weight: bold;">⏳ ' . __('Onay Bekliyor', 'role-custom') . '</span>' . $user_info . $date_info;
            $content .= '<br><div style="margin-top: 5px;">';
            $content .= '<button type="button" class="button button-small" onclick="approvePost(' . $post_id . ')" style="background: #00a32a; color: white; border: none; margin-right: 5px;">✓ ' . __('Onayla', 'role-custom') . '</button>';
            $content .= '<button type="button" class="button button-small" onclick="rejectPost(' . $post_id . ')" style="background: #d63638; color: white; border: none;">✗ ' . __('Reddet', 'role-custom') . '</button>';
            $content .= '</div>';
        } elseif ($post->post_status === 'publish') {
            $content = '<span style="color: #00a32a; font-weight: bold;">✓ ' . __('Yayınlanmış', 'role-custom') . '</span>';
        } elseif ($post->post_status === 'draft') {
            $content = '<span style="color: #666; font-weight: bold;">📝 ' . __('Taslak', 'role-custom') . '</span>';
        } else {
            $content = '<span style="color: #666;">' . ucfirst($post->post_status) . '</span>';
        }

        echo $content;
    }

    /**
     * Nitelik onaylama JavaScript'i ekle - Ayrı tablo sistemi
     */
    public function add_attribute_approval_js() {
        $screen = get_current_screen();

        // Sadece nitelik sayfasında
        if (!$screen || $screen->id !== 'product_page_product_attributes') {
            return;
        }

        // Pending nitelikleri al
        global $wpdb;
        $current_user_id = get_current_user_id();
        $is_admin = current_user_can('manage_options');

        if ($is_admin) {
            // Admin için tüm pending nitelikleri al
            $pending_attributes = $wpdb->get_results(
                "SELECT * FROM {$wpdb->prefix}woocommerce_attribute_taxonomies
                 WHERE attribute_id IN (
                     SELECT REPLACE(option_name, '_attribute_', '')
                     FROM {$wpdb->options}
                     WHERE option_name LIKE '_attribute_%_status'
                     AND option_value = 'pending'
                 )"
            );
        } else {
            // Eğitmen için sadece kendi oluşturduğu pending nitelikleri al
            $pending_attributes = $wpdb->get_results(
                $wpdb->prepare(
                    "SELECT * FROM {$wpdb->prefix}woocommerce_attribute_taxonomies
                     WHERE attribute_id IN (
                         SELECT REPLACE(option_name, '_attribute_', '')
                         FROM {$wpdb->options}
                         WHERE option_name LIKE '_attribute_%_status'
                         AND option_value = 'pending'
                         AND REPLACE(option_name, '_status', '_created_by') IN (
                             SELECT option_name FROM {$wpdb->options}
                             WHERE option_value = %s
                         )
                     )",
                    $current_user_id
                )
            );
        }

        ?>
        <script type="text/javascript">
        jQuery(document).ready(function($) {
            <?php if (!empty($pending_attributes)): ?>
                // Onay bekleyen nitelikler tablosu oluştur
                var pendingTableHtml = '<div id="pending-attributes-section" style="margin-bottom: 30px;">' +
                    '<h3 style="color: #d63638; margin-bottom: 15px;">⏳ <?php echo esc_js(__('Onay Bekleyen Nitelikler', 'role-custom')); ?></h3>' +
                    '<table class="widefat pending-attributes-table wp-list-table" style="border: 2px solid #ffc107; background-color: #fff3cd;">' +
                    '<thead style="background-color: #ffc107;">' +
                    '<tr>' +
                    '<th scope="col" style="padding: 10px; font-weight: bold;"><?php echo esc_js(__('Nitelik Adı', 'role-custom')); ?></th>' +
                    '<th scope="col" style="padding: 10px; font-weight: bold;"><?php echo esc_js(__('Slug', 'role-custom')); ?></th>' +
                    '<th scope="col" style="padding: 10px; font-weight: bold;"><?php echo esc_js(__('Tür', 'role-custom')); ?></th>' +
                    <?php if ($is_admin): ?>
                    '<th scope="col" style="padding: 10px; font-weight: bold;"><?php echo esc_js(__('Oluşturan', 'role-custom')); ?></th>' +
                    <?php endif; ?>
                    '<th scope="col" style="padding: 10px; font-weight: bold;"><?php echo esc_js(__('Tarih', 'role-custom')); ?></th>' +
                    '<th scope="col" style="padding: 10px; font-weight: bold;"><?php echo esc_js(__('Durum', 'role-custom')); ?></th>' +
                    <?php if ($is_admin): ?>
                    '<th scope="col" style="padding: 10px; font-weight: bold;"><?php echo esc_js(__('İşlemler', 'role-custom')); ?></th>' +
                    <?php endif; ?>
                    '</tr>' +
                    '</thead>' +
                    '<tbody>';

                <?php foreach ($pending_attributes as $attribute): ?>
                    <?php
                    $created_by = get_option('_attribute_' . $attribute->attribute_id . '_created_by', '');
                    $created_date = get_option('_attribute_' . $attribute->attribute_id . '_created_date', '');

                    $user_info = '';
                    if ($created_by) {
                        $user = get_user_by('id', $created_by);
                        if ($user) {
                            $user_info = $user->display_name;
                        }
                    }

                    $date_info = '';
                    if ($created_date) {
                        $date_info = date('d.m.Y H:i', strtotime($created_date));
                    }
                    ?>

                    pendingTableHtml += '<tr style="background-color: #fff3cd;">' +
                        '<td style="padding: 10px;"><strong><?php echo esc_js($attribute->attribute_label); ?></strong></td>' +
                        '<td style="padding: 10px;"><?php echo esc_js($attribute->attribute_name); ?></td>' +
                        '<td style="padding: 10px;"><?php echo esc_js($attribute->attribute_type); ?></td>' +
                        <?php if ($is_admin): ?>
                        '<td style="padding: 10px;"><?php echo esc_js($user_info); ?></td>' +
                        <?php endif; ?>
                        '<td style="padding: 10px;"><?php echo esc_js($date_info); ?></td>' +
                        '<td style="padding: 10px;"><span style="color: #856404; font-weight: bold;">⏳ <?php echo esc_js(__('Onay Bekliyor', 'role-custom')); ?></span></td>' +
                        <?php if ($is_admin): ?>
                        '<td style="padding: 10px;">' +
                        '<button type="button" class="button button-primary" onclick="approveAttribute(<?php echo $attribute->attribute_id; ?>)" style="margin-right: 5px;">✓ <?php echo esc_js(__('Onayla', 'role-custom')); ?></button>' +
                        '<button type="button" class="button" onclick="rejectAttribute(<?php echo $attribute->attribute_id; ?>)">✗ <?php echo esc_js(__('Reddet', 'role-custom')); ?></button>' +
                        '</td>' +
                        <?php endif; ?>
                        '</tr>';
                <?php endforeach; ?>

                pendingTableHtml += '</tbody></table>' +
                    '<p style="color: #856404; font-style: italic; margin-top: 10px;">' +
                    <?php if ($is_admin): ?>
                    '<?php echo esc_js(__('Bu nitelikler onaylanana kadar ürün varyasyonlarında kullanılamaz. Onaylamak için yukarıdaki butonları kullanın.', 'role-custom')); ?>' +
                    <?php else: ?>
                    '<?php echo esc_js(__('Bu nitelikler admin onayı bekliyor. Onaylandıktan sonra ürün varyasyonlarında kullanılabilir.', 'role-custom')); ?>' +
                    <?php endif; ?>
                    '</p>' +
                    '</div>';

                // Ana tablo container'ını bul ve pending tablosunu üstüne ekle
                var mainTableContainer = $('#col-container');
                if (mainTableContainer.length > 0) {
                    mainTableContainer.before(pendingTableHtml);
                }
            <?php endif; ?>
        });

        function approveAttribute(attributeId) {
            if (!confirm('<?php echo esc_js(__('Bu niteliği onaylamak istediğinizden emin misiniz?', 'role-custom')); ?>')) {
                return;
            }

            jQuery.post(ajaxurl, {
                action: 'approve_attribute',
                attribute_id: attributeId
            }, function(response) {
                if (response.success) {
                    alert(response.data.message);
                    location.reload();
                } else {
                    alert('<?php echo esc_js(__('Hata oluştu.', 'role-custom')); ?>');
                }
            });
        }

        function rejectAttribute(attributeId) {
            if (!confirm('<?php echo esc_js(__('Bu niteliği reddetmek ve silmek istediğinizden emin misiniz? Bu işlem geri alınamaz.', 'role-custom')); ?>')) {
                return;
            }

            jQuery.post(ajaxurl, {
                action: 'reject_attribute',
                attribute_id: attributeId
            }, function(response) {
                if (response.success) {
                    alert(response.data.message);
                    location.reload();
                } else {
                    alert('<?php echo esc_js(__('Hata oluştu.', 'role-custom')); ?>');
                }
            });
        }
        </script>
        <?php
    }

    /**
     * Post onaylama JavaScript'i ekle
     */
    public function add_post_approval_js() {
        $screen = get_current_screen();

        // Sadece post düzenleme sayfasında ve admin kullanıcılar için
        if (!$screen || $screen->id !== 'edit-post' || !current_user_can('manage_options')) {
            return;
        }

        ?>
        <script type="text/javascript">
        function approvePost(postId) {
            if (!confirm('<?php echo esc_js(__('Bu yazıyı onaylamak ve yayınlamak istediğinizden emin misiniz?', 'role-custom')); ?>')) {
                return;
            }

            jQuery.post(ajaxurl, {
                action: 'approve_post',
                post_id: postId
            }, function(response) {
                if (response.success) {
                    alert(response.data.message);
                    location.reload();
                } else {
                    alert('<?php echo esc_js(__('Hata oluştu.', 'role-custom')); ?>');
                }
            });
        }

        function rejectPost(postId) {
            if (!confirm('<?php echo esc_js(__('Bu yazıyı reddetmek istediğinizden emin misiniz? Yazı taslak durumuna alınacak.', 'role-custom')); ?>')) {
                return;
            }

            jQuery.post(ajaxurl, {
                action: 'reject_post',
                post_id: postId
            }, function(response) {
                if (response.success) {
                    alert(response.data.message);
                    location.reload();
                } else {
                    alert('<?php echo esc_js(__('Hata oluştu.', 'role-custom')); ?>');
                }
            });
        }
        </script>
        <?php
    }

    /**
     * AJAX: Niteliği onayla
     */
    public function ajax_approve_attribute() {
        // Güvenlik kontrolü
        if (!current_user_can('manage_options')) {
            wp_die(__('Yetkiniz yok.', 'role-custom'));
        }

        $attribute_id = intval($_POST['attribute_id']);
        if (!$attribute_id) {
            wp_die(__('Geçersiz nitelik ID.', 'role-custom'));
        }

        // Meta bilgileri güncelle
        update_option('_attribute_' . $attribute_id . '_status', 'approved');
        update_option('_attribute_' . $attribute_id . '_approved_by', get_current_user_id());
        update_option('_attribute_' . $attribute_id . '_approved_date', current_time('mysql'));

        // Cache'i temizle ki filtreleme çalışsın
        delete_transient('wc_attribute_taxonomies');

        wp_send_json_success([
            'message' => __('Nitelik başarıyla onaylandı ve artık kullanılabilir.', 'role-custom')
        ]);
    }

    /**
     * AJAX: Niteliği reddet
     */
    public function ajax_reject_attribute() {
        // Güvenlik kontrolü
        if (!current_user_can('manage_options')) {
            wp_die(__('Yetkiniz yok.', 'role-custom'));
        }

        $attribute_id = intval($_POST['attribute_id']);
        if (!$attribute_id) {
            wp_die(__('Geçersiz nitelik ID.', 'role-custom'));
        }

        // Niteliği sil
        $result = wc_delete_attribute($attribute_id);

        if (is_wp_error($result)) {
            wp_send_json_error([
                'message' => __('Nitelik silinirken hata oluştu.', 'role-custom')
            ]);
        } else {
            // Meta bilgileri temizle
            delete_option('_attribute_' . $attribute_id . '_status');
            delete_option('_attribute_' . $attribute_id . '_created_by');
            delete_option('_attribute_' . $attribute_id . '_created_date');
            delete_option('_attribute_' . $attribute_id . '_original_data');

            wp_send_json_success([
                'message' => __('Nitelik başarıyla reddedildi ve silindi.', 'role-custom')
            ]);
        }
    }

    /**
     * AJAX: Postu onayla
     */
    public function ajax_approve_post() {
        // Güvenlik kontrolü
        if (!current_user_can('manage_options')) {
            wp_die(__('Yetkiniz yok.', 'role-custom'));
        }

        $post_id = intval($_POST['post_id']);
        if (!$post_id) {
            wp_die(__('Geçersiz post ID.', 'role-custom'));
        }

        // Post durumunu güncelle
        $result = wp_update_post([
            'ID' => $post_id,
            'post_status' => 'publish'
        ]);

        if (is_wp_error($result)) {
            wp_send_json_error([
                'message' => __('Post onaylanırken hata oluştu.', 'role-custom')
            ]);
        }

        // Meta bilgileri güncelle
        update_post_meta($post_id, '_post_approved_by', get_current_user_id());
        update_post_meta($post_id, '_post_approved_date', current_time('mysql'));

        wp_send_json_success([
            'message' => __('Post başarıyla onaylandı ve yayınlandı.', 'role-custom')
        ]);
    }

    /**
     * AJAX: Postu reddet
     */
    public function ajax_reject_post() {
        // Güvenlik kontrolü
        if (!current_user_can('manage_options')) {
            wp_die(__('Yetkiniz yok.', 'role-custom'));
        }

        $post_id = intval($_POST['post_id']);
        if (!$post_id) {
            wp_die(__('Geçersiz post ID.', 'role-custom'));
        }

        // Post durumunu güncelle (draft yap)
        $result = wp_update_post([
            'ID' => $post_id,
            'post_status' => 'draft'
        ]);

        if (is_wp_error($result)) {
            wp_send_json_error([
                'message' => __('Post reddedilirken hata oluştu.', 'role-custom')
            ]);
        }

        // Meta bilgileri güncelle
        update_post_meta($post_id, '_post_rejected_by', get_current_user_id());
        update_post_meta($post_id, '_post_rejected_date', current_time('mysql'));

        wp_send_json_success([
            'message' => __('Post başarıyla reddedildi ve taslak durumuna alındı.', 'role-custom')
        ]);
    }

    /**
     * Eski superole sistemini temizle
     */
    public function cleanup_old_superole_instructor_system() {
        // Sadece admin kullanıcılar için ve sadece bir kez çalıştır
        if (!current_user_can('manage_options')) {
            return;
        }

        // Bu temizleme işlemini sadece bir kez yap
        $cleanup_done = get_option('role_custom_superole_cleanup_done', false);
        if ($cleanup_done) {
            return;
        }

        // Eski superole rolünü kaldır
        if (get_role('superole')) {
            // Superole kullanıcılarını tutor_instructor rolüne geçir
            $superole_users = get_users(['role' => 'superole']);
            foreach ($superole_users as $user) {
                $user->remove_role('superole');
                $user->add_role('tutor_instructor');
            }

            // Superole rolünü kaldır
            remove_role('superole');
        }

        // İşlemin tamamlandığını kaydet
        update_option('role_custom_superole_cleanup_done', true);
    }




    
    /**
     * Eklenti etkinleştirme
     */
    public function activate() {
        // WordPress capabilities cache'ini temizle
        if (function_exists('wp_cache_delete')) {
            wp_cache_delete('user_roles', 'options');
        }

        // Flush rewrite rules
        flush_rewrite_rules();

        // Aktivasyon zamanını kaydet
        update_option('role_custom_activated', current_time('mysql'));

        // Başarı bildirimi için transient ayarla
        set_transient('role_custom_activated_notice', true, 30);
    }
    
    /**
     * Eklenti devre dışı bırakma
     */
    public function deactivate() {
        // Flush rewrite rules
        flush_rewrite_rules();

        // Deaktivasyon zamanını kaydet
        update_option('role_custom_deactivated', current_time('mysql'));
    }
    

    
    /**
     * Tutor LMS menülerini kısıtla
     * Tutor Instructor rolü için belirli menüler gizlenir
     */
    public function restrict_tutor_menus() {
        // Sadece Tutor Instructor rolündeki kullanıcılar için
        if (!$this->is_current_user_tutor_instructor()) {
            return;
        }

        global $submenu;

        // Tutor LMS alt menülerini kontrol et
        if (isset($submenu['tutor'])) {
            $restricted_slugs = [
                'tutor-tools',              // Araçlar
                'tutor_settings',           // Ayarlar
                'tutor-withdrawals',        // Para Çekme Talepleri
                'tutor_withdraw',           // Para Çekme (alternatif slug)
                'withdraw',                 // Para Çekme (kısa slug)
                'tutor-get-pro'            // Upgrade to Pro
            ];

            foreach ($submenu['tutor'] as $key => $menu_item) {
                $menu_slug = $menu_item[2];

                // Kısıtlı menüler listesinde varsa kaldır
                if (in_array($menu_slug, $restricted_slugs)) {
                    unset($submenu['tutor'][$key]);
                }
            }
        }
    }

    /**
     * Tutor LMS menü etiketlerini özelleştir
     * Ana menü "Tutor LMS" -> "Eğitimler"
     * Alt menü başlığı eski halinde kalır ("Courses")
     */
    public function customize_tutor_menu_labels() {
        // Sadece Tutor Instructor rolündeki kullanıcılar için
        if (!$this->is_current_user_tutor_instructor()) {
            return;
        }

        global $menu;

        // Ana Tutor LMS menüsünün başlığını "Eğitimler" olarak değiştir
        if (isset($menu)) {
            foreach ($menu as $key => $menu_item) {
                if (isset($menu_item[2]) && $menu_item[2] === 'tutor') {
                    // Ana menü başlığını değiştir
                    $menu[$key][0] = __('Eğitimler', 'role-custom');
                    break;
                }
            }
        }

        // Alt menü başlığı eski halinde kalır, değişiklik yapılmıyor
    }
    
    /**
     * İzin verilen Tutor LMS menü slug'larını döndür
     */
    private function get_allowed_tutor_menu_slugs() {
        // Tutor LMS admin menü analizi sonucu doğru slug'lar
        return [
            'tutor',                           // Kurslar (ana sayfa)
            'tutor-students',                  // Öğrenciler (Students_List::STUDENTS_LIST_PAGE)
            'tutor_announcements',             // Duyurular
            'question_answer',                 // Q&A (Question_Answers_List::QUESTION_ANSWER_PAGE)
            'tutor_quiz_attempts'              // Sınav Denemeleri (Quiz_Attempts_List::QUIZ_ATTEMPT_PAGE)
        ];
    }

    /**
     * WooCommerce menülerini kısıtla
     */
    public function restrict_woocommerce_menus() {
        // Sadece Tutor Instructor rolündeki kullanıcılar için
        if (!$this->is_current_user_tutor_instructor()) {
            return;
        }

        global $menu, $submenu;

        // 1. Ödemeler ana sekmesini gizle (farklı olası slug'lar)
        $payment_menu_slugs = [
            'wc-admin&path=/payments/connect',
            'admin.php?page=wc-admin&path=/payments/connect',
            'wc-admin&path=/payments',
            'admin.php?page=wc-admin&path=/payments',
            'woocommerce-payments',
            'wc-payments',
            'admin.php?page=wc-settings&tab=checkout',
            'wc-settings&tab=checkout'
        ];

        foreach ($payment_menu_slugs as $slug) {
            $this->remove_top_level_menu($slug);
            // WordPress'in remove_menu_page fonksiyonunu da kullan
            remove_menu_page($slug);
        }

        // Spesifik ödemeler menüsünü kaldır
        remove_menu_page('admin.php?page=wc-settings&tab=checkout');
        remove_menu_page('wc-settings&tab=checkout');

        // WooCommerce ana menüsünü kaldır
        remove_menu_page('woocommerce');
        $this->remove_top_level_menu('woocommerce');

        // 2. WooCommerce alt menülerini kısıtla
        if (isset($submenu['woocommerce'])) {
            $restricted_wc_slugs = $this->get_restricted_woocommerce_menu_slugs();

            foreach ($submenu['woocommerce'] as $key => $menu_item) {
                $menu_slug = $menu_item[2];

                // Kısıtlı menüler listesinde varsa kaldır
                if (in_array($menu_slug, $restricted_wc_slugs)) {
                    unset($submenu['woocommerce'][$key]);
                }

                // Ana menü haline getirilen menüleri de alt menüden kaldır
                if (in_array($menu_slug, $this->get_top_level_menu_slugs())) {
                    unset($submenu['woocommerce'][$key]);
                }
            }
        }
    }

    /**
     * Kısıtlı WooCommerce menü slug'larını döndür
     */
    private function get_restricted_woocommerce_menu_slugs() {
        return [
            'wc-settings',                     // Ayarlar
            'wc-status',                       // Durum
            'wc-addons',                       // Genişletme Paketleri
        ];
    }

    /**
     * Ana menü haline getirilen WooCommerce menü slug'larını döndür
     */
    private function get_top_level_menu_slugs() {
        return [
            'admin.php?page=wc-orders',       // Siparişler
            'users.php?role=customer',        // Müşteriler
            'admin.php?page=wc-reports',      // Raporlar
            'edit.php?post_type=shop_coupon', // Kuponlar
        ];
    }

    /**
     * Top-level menüyü kaldır
     */
    private function remove_top_level_menu($menu_slug) {
        global $menu;

        foreach ($menu as $key => $menu_item) {
            if (isset($menu_item[2]) && $menu_item[2] === $menu_slug) {
                unset($menu[$key]);
                break;
            }
        }
    }

    /**
     * Ödemeler menüsünü özel olarak kaldır
     */
    public function remove_payments_menu() {
        // Sadece Tutor Instructor rolündeki kullanıcılar için
        if (!$this->is_current_user_tutor_instructor()) {
            return;
        }

        global $menu;

        // Tüm menüleri kontrol et ve ödemeler ile ilgili olanları kaldır
        foreach ($menu as $key => $menu_item) {
            if (isset($menu_item[2])) {
                $menu_slug = $menu_item[2];
                $menu_title = isset($menu_item[0]) ? $menu_item[0] : '';

                // Menü slug'ında veya başlığında ödemeler/payments geçiyorsa kaldır
                if (
                    strpos($menu_slug, 'payments') !== false ||
                    strpos($menu_slug, 'checkout') !== false ||
                    strpos($menu_slug, 'PAYMENTS_MENU_ITEM') !== false ||
                    strpos($menu_title, 'Ödemeler') !== false ||
                    strpos($menu_title, 'Payments') !== false
                ) {
                    unset($menu[$key]);
                }
            }
        }

        // WordPress'in remove_menu_page fonksiyonunu da kullan
        $payment_pages = [
            'admin.php?page=wc-settings&tab=checkout',
            'wc-settings&tab=checkout',
            'woocommerce-payments',
            'wc-payments'
        ];

        foreach ($payment_pages as $page) {
            remove_menu_page($page);
        }
    }

    /**
     * Pazarlama menüsünü gizle
     */
    public function hide_marketing_menu() {
        // Sadece Tutor Instructor rolündeki kullanıcılar için
        if (!$this->is_current_user_tutor_instructor()) {
            return;
        }

        // Pazarlama menüsünü kaldır
        remove_menu_page('woocommerce-marketing');

        // Alternatif pazarlama menü slug'ları
        $marketing_menu_slugs = [
            'woocommerce-marketing',
            'admin.php?page=wc-admin&path=/marketing',
            'wc-admin&path=/marketing',
            'marketing'
        ];

        foreach ($marketing_menu_slugs as $slug) {
            remove_menu_page($slug);
        }

        // WooCommerce Raporlar menüsünü kaldır
        remove_menu_page('admin.php?page=wc-reports');
        remove_menu_page('wc-reports');
    }

    /**
     * WooCommerce ürün kategorileri ve etiketleri menülerini gizle
     * NOT: Değerlendirmeler sekmesi artık gizlenmiyor - kullanıcı talebi üzerine görünür hale getirildi
     */
    public function hide_product_taxonomies() {
        // Sadece Tutor Instructor rolündeki kullanıcılar için
        if (!$this->is_current_user_tutor_instructor()) {
            return;
        }

        // WooCommerce aktif değilse çık
        if (!class_exists('WooCommerce')) {
            return;
        }

        global $submenu;

        // Ürünler menüsü altındaki kategoriler ve etiketler sekmelerini kaldır
        // NOT: Değerlendirmeler (product-reviews) sekmesi artık gizlenmiyor
        if (isset($submenu['edit.php?post_type=product'])) {
            foreach ($submenu['edit.php?post_type=product'] as $key => $menu_item) {
                $menu_slug = $menu_item[2];

                // Sadece kategoriler ve etiketler menü slug'larını kontrol et
                // Değerlendirmeler sekmesini (product-reviews) gizleme listesinden çıkardık
                if (
                    $menu_slug === 'edit-tags.php?taxonomy=product_cat&amp;post_type=product' ||
                    $menu_slug === 'edit-tags.php?taxonomy=product_cat&post_type=product' ||
                    $menu_slug === 'edit-tags.php?taxonomy=product_tag&amp;post_type=product' ||
                    $menu_slug === 'edit-tags.php?taxonomy=product_tag&post_type=product' ||
                    strpos($menu_slug, 'taxonomy=product_cat') !== false ||
                    strpos($menu_slug, 'taxonomy=product_tag') !== false
                ) {
                    unset($submenu['edit.php?post_type=product'][$key]);
                }
            }
        }

        // Doğrudan menü sayfalarını da kaldır
        remove_submenu_page('edit.php?post_type=product', 'edit-tags.php?taxonomy=product_cat&post_type=product');
        remove_submenu_page('edit.php?post_type=product', 'edit-tags.php?taxonomy=product_tag&post_type=product');

        // Değerlendirmeler sekmesinin görünür olduğundan emin ol
        // WooCommerce değerlendirmeler sekmesi için yetki kontrolü
        if (current_user_can('moderate_comments') || current_user_can('edit_products')) {
            // Değerlendirmeler sekmesi zaten WooCommerce tarafından ekleniyor
            // Burada sadece yetki kontrolü yapıyoruz
        }
    }

    /**
     * WooCommerce alt menülerini ana menü haline getir
     */
    public function create_woocommerce_top_level_menus() {
        // Sadece Tutor Instructor rolündeki kullanıcılar için
        if (!$this->is_current_user_tutor_instructor()) {
            return;
        }

        // WooCommerce aktif değilse çık
        if (!class_exists('WooCommerce')) {
            return;
        }

        // 1. Siparişler ana menüsü
        add_menu_page(
            __('Siparişler', 'role-custom'),           // Sayfa başlığı
            __('Siparişler', 'role-custom'),           // Menü başlığı
            'edit_shop_orders',                        // Yetki
            'admin.php?page=wc-orders',                // Menü slug'ı (düzeltildi)
            '',                                        // Fonksiyon (boş bırak, redirect olacak)
            'dashicons-clipboard',                     // İkon
            56                                         // Pozisyon (WooCommerce'den sonra)
        );

        // 2. Müşteriler ana menüsü
        add_menu_page(
            __('Müşteriler', 'role-custom'),           // Sayfa başlığı
            __('Müşteriler', 'role-custom'),           // Menü başlığı
            'list_users',                              // Yetki
            'users.php?role=customer',                 // Menü slug'ı
            '',                                        // Fonksiyon (boş bırak, redirect olacak)
            'dashicons-groups',                        // İkon
            57                                         // Pozisyon
        );

        // 3. Kuponlar ana menüsü
        add_menu_page(
            __('Kuponlar', 'role-custom'),             // Sayfa başlığı
            __('Kuponlar', 'role-custom'),             // Menü başlığı
            'edit_shop_coupons',                       // Yetki
            'edit.php?post_type=shop_coupon',          // Menü slug'ı
            '',                                        // Fonksiyon (boş bırak, redirect olacak)
            'dashicons-tickets-alt',                   // İkon
            58                                         // Pozisyon
        );

        // Reports menü sayfası için hook'lar - bu menü pozisyon 10001'de ekleniyor
        // Bu yüzden diğer menüleri ondan sonraki pozisyonlara yerleştiriyoruz

        // 4. Kütüphane ana menüsü (Role Custom Reports'un altına taşındı)
        add_menu_page(
            __('Kütüphane', 'role-custom'),            // Sayfa başlığı
            __('Kütüphane', 'role-custom'),            // Menü başlığı
            'upload_files',                            // Yetki
            'upload.php',                              // Menü slug'ı
            '',                                        // Fonksiyon (boş bırak, redirect olacak)
            'dashicons-admin-media',                   // İkon
            10002                                      // Pozisyon (Reports'un hemen altında)
        );

        // 5. Postlar ana menüsü (Kütüphanenin altına taşındı)
        add_menu_page(
            __('Postlar', 'role-custom'),              // Sayfa başlığı
            __('Postlar', 'role-custom'),              // Menü başlığı
            'edit_posts',                              // Yetki
            'edit.php',                                // Menü slug'ı
            '',                                        // Fonksiyon (boş bırak, redirect olacak)
            'dashicons-admin-post',                    // İkon
            10003                                      // Pozisyon (Kütüphanenin altında)
        );
    }

    /**
     * Dashboard'dan Tutor LMS'e yönlendir
     */
    public function redirect_dashboard_to_tutor() {
        // Sadece Tutor Instructor rolündeki kullanıcılar için
        if (!$this->is_current_user_tutor_instructor()) {
            return;
        }

        // Sadece admin panelinde
        if (!is_admin()) {
            return;
        }

        // Dashboard sayfasında mıyız kontrol et
        global $pagenow;
        if ($pagenow === 'index.php' && !isset($_GET['page'])) {
            // Tutor LMS aktif mi kontrol et
            if (class_exists('TUTOR\Tutor')) {
                // Tutor LMS ana sayfasına yönlendir
                wp_redirect(admin_url('admin.php?page=tutor'));
                exit;
            }
        }
    }
    
    /**
     * Mevcut kullanıcının Tutor Instructor rolünde olup olmadığını kontrol et
     * Admin kullanıcıları hiçbir zaman etkilenmez
     */
    private function is_current_user_tutor_instructor() {
        $current_user = wp_get_current_user();

        // Admin kullanıcıları hiçbir zaman etkilenmez
        if (in_array('administrator', $current_user->roles)) {
            return false;
        }

        return in_array(self::TUTOR_INSTRUCTOR_ROLE, $current_user->roles);
    }

    /**
     * Belirtilen kullanıcının Tutor Instructor rolünde olup olmadığını kontrol et
     * Admin kullanıcıları hiçbir zaman etkilenmez
     */
    private function is_user_tutor_instructor($user_id) {
        $user = get_user_by('ID', $user_id);
        if (!$user) {
            return false;
        }

        // Admin kullanıcıları hiçbir zaman etkilenmez
        if (in_array('administrator', $user->roles)) {
            return false;
        }

        // Tutor Instructor rolünde mi kontrol et
        return in_array(self::TUTOR_INSTRUCTOR_ROLE, $user->roles);
    }

    /**
     * WooCommerce ve Tutor LMS yetkilerini ayarla
     */
    public function setup_woocommerce_capabilities() {
        // Sadece admin kullanıcılar bu işlemi yapabilir
        if (!current_user_can('manage_options')) {
            return;
        }

        // Tutor Instructor rolü için WooCommerce yetkilerini kontrol et ve gerekirse ekle
        $role = get_role(self::TUTOR_INSTRUCTOR_ROLE);
        if ($role) {
            // WooCommerce yetkileri
            $woocommerce_caps = [
                // Temel WooCommerce yetkileri
                'manage_woocommerce',
                'view_woocommerce_reports',

                // Sipariş yönetimi
                'edit_shop_orders',
                'edit_others_shop_orders',
                'publish_shop_orders',
                'read_shop_orders',
                'delete_shop_orders',
                'delete_others_shop_orders',
                'read_private_shop_orders',
                'edit_private_shop_orders',
                'delete_private_shop_orders',

                // Ürün yönetimi
                'edit_products',
                'edit_others_products',
                'publish_products',
                'read_products',
                'delete_products',
                'delete_others_products',
                'read_private_products',
                'edit_private_products',
                'delete_private_products',

                // Ürün kategorileri ve etiketleri
                'manage_product_terms',
                'edit_product_terms',
                'delete_product_terms',
                'assign_product_terms',

                // Kupon yönetimi
                'edit_shop_coupons',
                'edit_others_shop_coupons',
                'publish_shop_coupons',
                'read_shop_coupons',
                'delete_shop_coupons',
                'delete_others_shop_coupons',
                'read_private_shop_coupons',
                'edit_private_shop_coupons',
                'delete_private_shop_coupons',

                // Webhook yönetimi
                'edit_shop_webhooks',
                'edit_others_shop_webhooks',
                'publish_shop_webhooks',
                'read_shop_webhooks',
                'delete_shop_webhooks',
                'delete_others_shop_webhooks',
                'read_private_shop_webhooks',
                'edit_private_shop_webhooks',
                'delete_private_shop_webhooks',

                // Değerlendirmeler yönetimi (WooCommerce Product Reviews)
                'moderate_comments',               // Değerlendirmeleri görüntüleme ve moderasyon
                'edit_comment',                    // Değerlendirmeleri düzenleme
                'delete_comment',                  // Değerlendirmeleri silme
            ];

            // WooCommerce yetkilerini ekle
            foreach ($woocommerce_caps as $cap) {
                if (!$role->has_cap($cap)) {
                    $role->add_cap($cap);
                }
            }
        }
    }
    
    /**
     * Kısıtlı menüleri gizlemek için CSS
     */
    public function hide_restricted_menus_css() {
        if (!$this->is_current_user_tutor_instructor()) {
            return;
        }

        // Tutor LMS ve WooCommerce menuleri icin CSS
        echo '<style>
            /* Tutor LMS restricted menus - Hide for Tutor Instructor role */
            #adminmenu .wp-submenu li a[href*="tutor-new-feature"],
            #adminmenu .wp-submenu li a[href*="create-course"],
            #adminmenu .wp-submenu li a[href*="tutor-themes"],
            #adminmenu .wp-submenu li a[href*="course-category"],
            #adminmenu .wp-submenu li a[href*="course-tag"],
            #adminmenu .wp-submenu li a[href*="tutor-instructors"],
            #adminmenu .wp-submenu li a[href*="tutor-addons"],
            #adminmenu .wp-submenu li a[href*="tutor-tools"],
            #adminmenu .wp-submenu li a[href*="tutor_settings"],
            #adminmenu .wp-submenu li a[href*="tutor-get-pro"],
            #adminmenu .wp-submenu li a[href*="tutor_orders"],
            #adminmenu .wp-submenu li a[href*="tutor_coupons"],
            #adminmenu .wp-submenu li a[href*="tutor-withdrawals"],
            #adminmenu .wp-submenu li a[href*="tutor_withdraw"],
            #adminmenu .wp-submenu li a[href*="withdraw"],

            /* WooCommerce restricted menus - Hide for Tutor Instructor role */
            #adminmenu .wp-submenu li a[href*="wc-settings"],
            #adminmenu .wp-submenu li a[href*="wc-status"],
            #adminmenu .wp-submenu li a[href*="wc-addons"],

            /* Hide Payments main menu - Various possible slugs */
            #adminmenu li a[href*="payments/connect"],
            #adminmenu li a[href*="wc-admin&path=/payments"],
            #adminmenu li a[href*="woocommerce-payments"],
            #adminmenu li a[href*="wc-payments"],
            #adminmenu li a[href*="wc-settings&tab=checkout"],
            #adminmenu li.toplevel_page_wc-admin-path-payments,
            #adminmenu li[id*="payments"],
            #toplevel_page_admin-page-wc-settings-tab-checkout-from-PAYMENTS_MENU_ITEM,
            #adminmenu #toplevel_page_admin-page-wc-settings-tab-checkout-from-PAYMENTS_MENU_ITEM,
            li[id*="checkout"],
            li[id*="PAYMENTS_MENU_ITEM"],
            #adminmenu li[class*="checkout"],
            #adminmenu li[class*="payments"],

            /* Additional specific WooCommerce menu items to hide */
            #toplevel_page_woocommerce > ul > li.wp-first-item > a,
            #toplevel_page_woocommerce > ul > li:nth-child(6),
            #toplevel_page_woocommerce-marketing > ul > li.wp-first-item.current > a,

            /* Hide WooCommerce submenu items by href content */
            #adminmenu #toplevel_page_woocommerce ul li a[href*="wc-settings"],
            #adminmenu #toplevel_page_woocommerce ul li a[href*="wc-status"],
            #adminmenu #toplevel_page_woocommerce ul li a[href*="wc-addons"],

            /* Hide submenu items that are now top-level menus */
            #adminmenu #toplevel_page_woocommerce ul li a[href*="edit.php?post_type=shop_order"],
            #adminmenu #toplevel_page_woocommerce ul li a[href*="shop_order"],
            #adminmenu #toplevel_page_woocommerce ul li a[href*="wc-orders"],
            #adminmenu #toplevel_page_woocommerce ul li a[href*="wc-reports"],
            #adminmenu #toplevel_page_woocommerce ul li a[href*="users.php?role=customer"],
            #adminmenu #toplevel_page_woocommerce ul li a[href*="edit.php?post_type=shop_coupon"],
            #adminmenu #toplevel_page_woocommerce ul li a[href*="shop_coupon"],

            /* Hide from Marketing submenu */
            #adminmenu #toplevel_page_woocommerce-marketing ul li a[href*="edit.php?post_type=shop_coupon"],
            #adminmenu #toplevel_page_woocommerce-marketing ul li a[href*="shop_coupon"],

            /* Hide WooCommerce main menu */
            #toplevel_page_woocommerce > a > div.wp-menu-name,
            #toplevel_page_woocommerce > a,
            #toplevel_page_woocommerce,

            /* Hide WordPress Tools menu */
            #menu-tools > a,
            #menu-tools,

            /* Hide WordPress Dashboard menu */
            #menu-dashboard > a,
            #menu-dashboard,

            /* Hide WordPress Users menu */
            #menu-users > a,
            #menu-users,

            /* Hide WordPress Posts menu */
            #menu-posts > a,
            #menu-posts,

            /* Hide specific submenus that are now top-level menus */
            #toplevel_page_upload > ul > li.wp-first-item,
            #toplevel_page_edit > ul > li.wp-first-item,

            /* Hide all submenus from new top-level Library and Posts menus */
            #adminmenu li a[href="upload.php"] + .wp-submenu,
            #adminmenu li a[href="edit.php"] + .wp-submenu,
            #adminmenu li a[href="upload.php"] ~ ul,
            #adminmenu li a[href="edit.php"] ~ ul,

            /* Hide Marketing menu */
            #toplevel_page_woocommerce-marketing > a,
            #toplevel_page_woocommerce-marketing,
            #adminmenu li a[href*="woocommerce-marketing"],
            #adminmenu li a[href*="wc-admin&path=/marketing"],
            #adminmenu li a[href*="marketing"],

            /* Hide WooCommerce Reports menu */
            #toplevel_page_admin-page-wc-reports,
            #toplevel_page_admin-page-wc-reports > a,
            #adminmenu li a[href*="admin-page-wc-reports"],

            /* Hide WooCommerce Analytics menu */
            #toplevel_page_wc-admin-path--analytics-overview,
            #toplevel_page_wc-admin-path--analytics-overview > a,
            #adminmenu li a[href*="wc-admin&path=/analytics"],

            /* Hide WooCommerce Product Categories and Tags menus */
            #adminmenu .wp-submenu li a[href*="taxonomy=product_cat"],
            #adminmenu .wp-submenu li a[href*="taxonomy=product_tag"],
            #adminmenu #toplevel_page_edit-post_type-product ul li a[href*="taxonomy=product_cat"],
            #adminmenu #toplevel_page_edit-post_type-product ul li a[href*="taxonomy=product_tag"],

            /* Hide specific menu items that create empty spaces */
            #adminmenu > li:nth-child(12),
            #adminmenu > li:nth-child(10),
            #adminmenu > li:nth-child(8),
            #adminmenu > li:nth-child(3),
            #menu-media,
            #menu-media > a,
            #adminmenu > li.wp-not-current-submenu.wp-menu-separator.woocommerce {
                display: none !important;
            }

            /* Hide count numbers in WordPress admin panel for tutor instructor role */
            .subsubsub a .count,
            .subsubsub a.current .count {
                display: none !important;
            }

            /* Hide WordPress admin bar items for tutor instructor role */
            #wp-admin-bar-wp-logo,
            #wp-admin-bar-woocommerce-site-visibility-badge,
            #wp-admin-bar-archive {
                display: none !important;
            }

            /* Hide WooCommerce page header and help button for tutor instructor role */
            #woocommerce-embedded-root > div.woocommerce-layout > div.woocommerce-layout__header,
            #contextual-help-link-wrap {
                display: none !important;
            }

            /* Fix active menu styling for custom top-level menus */
            #adminmenu li.menu-top.toplevel_page_admin-page-wc-orders.current > a,
            #adminmenu li.menu-top.toplevel_page_admin-page-wc-orders.wp-has-current-submenu > a,
            #adminmenu li.menu-top.toplevel_page_users-php-role-customer.current > a,
            #adminmenu li.menu-top.toplevel_page_users-php-role-customer.wp-has-current-submenu > a,
            #adminmenu li.menu-top.toplevel_page_admin-page-wc-reports.current > a,
            #adminmenu li.menu-top.toplevel_page_admin-page-wc-reports.wp-has-current-submenu > a,
            #adminmenu li.menu-top.toplevel_page_edit-php-post_type-shop_coupon.current > a,
            #adminmenu li.menu-top.toplevel_page_edit-php-post_type-shop_coupon.wp-has-current-submenu > a {
                background-color: #0073aa !important;
                color: #fff !important;
            }

            /* Active menu arrow for custom menus */
            #adminmenu li.menu-top.toplevel_page_admin-page-wc-orders.current:after,
            #adminmenu li.menu-top.toplevel_page_admin-page-wc-orders.wp-has-current-submenu:after,
            #adminmenu li.menu-top.toplevel_page_users-php-role-customer.current:after,
            #adminmenu li.menu-top.toplevel_page_users-php-role-customer.wp-has-current-submenu:after,
            #adminmenu li.menu-top.toplevel_page_admin-page-wc-reports.current:after,
            #adminmenu li.menu-top.toplevel_page_admin-page-wc-reports.wp-has-current-submenu:after,
            #adminmenu li.menu-top.toplevel_page_edit-php-post_type-shop_coupon.current:after,
            #adminmenu li.menu-top.toplevel_page_edit-php-post_type-shop_coupon.wp-has-current-submenu:after {
                border-right-color: #f1f1f1 !important;
            }
        </style>';

        // JavaScript ile dinamik olarak ödemeler menüsünü gizle
        echo '<script>
            jQuery(document).ready(function($) {
                // Ödemeler menüsünü gizle
                $("#toplevel_page_admin-page-wc-settings-tab-checkout-from-PAYMENTS_MENU_ITEM").hide();
                $("li[id*=\"checkout\"]").hide();
                $("li[id*=\"PAYMENTS_MENU_ITEM\"]").hide();
                $("li[id*=\"payments\"]").hide();
                $("a[href*=\"wc-settings&tab=checkout\"]").parent().hide();
                $("a[href*=\"payments\"]").parent().hide();

                // Spesifik WooCommerce menü öğelerini gizle
                $("#toplevel_page_woocommerce > ul > li.wp-first-item > a").hide();
                $("#toplevel_page_woocommerce > ul > li:nth-child(6)").hide();
                $("#toplevel_page_woocommerce-marketing > ul > li.wp-first-item.current > a").hide();

                // WooCommerce alt menülerinde belirli öğeleri gizle
                $("#toplevel_page_woocommerce ul li").each(function() {
                    var $link = $(this).find("a");
                    var href = $link.attr("href") || "";
                    var text = $link.text().trim();

                    // Ayarlar, Durum, Genişletme Paketleri menülerini gizle
                    if (href.includes("wc-settings") ||
                        href.includes("wc-status") ||
                        href.includes("wc-addons") ||
                        text === "Ayarlar" ||
                        text === "Settings" ||
                        text === "Durum" ||
                        text === "Status" ||
                        text === "Genişletme Paketleri" ||
                        text === "Extensions") {
                        $(this).hide();
                    }

                    // Ana menü haline getirilen alt menüleri gizle
                    if (href.includes("shop_order") ||
                        href.includes("wc-orders") ||
                        href.includes("wc-reports") ||
                        href.includes("users.php?role=customer") ||
                        href.includes("shop_coupon") ||
                        text === "Siparişler" ||
                        text === "Orders" ||
                        text === "Müşteriler" ||
                        text === "Customers" ||
                        text === "Raporlar" ||
                        text === "Reports" ||
                        text === "Kuponlar" ||
                        text === "Coupons") {
                        $(this).hide();
                    }
                });

                // Ortamlar ve Yazılar menülerinden belirli alt sekmeleri gizle
                $("#toplevel_page_upload ul li").each(function() {
                    var $link = $(this).find("a");
                    var href = $link.attr("href") || "";
                    var text = $link.text().trim();

                    // Kütüphane alt sekmesini gizle (ana menü haline getirildi)
                    if (href.includes("upload.php") && !href.includes("?") ||
                        text === "Kütüphane" ||
                        text === "Library") {
                        $(this).hide();
                    }
                });

                $("#toplevel_page_edit ul li").each(function() {
                    var $link = $(this).find("a");
                    var href = $link.attr("href") || "";
                    var text = $link.text().trim();

                    // Postlar alt sekmesini gizle (ana menü haline getirildi)
                    if (href === "edit.php" ||
                        text === "Tüm Yazılar" ||
                        text === "All Posts") {
                        $(this).hide();
                    }
                });

                // WooCommerce ana menüsünü gizle
                $("#toplevel_page_woocommerce").hide();
                $("#toplevel_page_woocommerce > a").hide();
                $("#toplevel_page_woocommerce > a > div.wp-menu-name").hide();

                // WordPress Araçlar menüsünü gizle
                $("#menu-tools").hide();
                $("#menu-tools > a").hide();

                // WordPress Dashboard menüsünü gizle
                $("#menu-dashboard").hide();
                $("#menu-dashboard > a").hide();

                // WordPress Kullanıcılar menüsünü gizle
                $("#menu-users").hide();
                $("#menu-users > a").hide();

                // WordPress Yazılar menüsünü gizle
                $("#menu-posts").hide();
                $("#menu-posts > a").hide();

                // Ortamlar menüsünden Kütüphane alt sekmesini gizle
                $("#toplevel_page_upload > ul > li.wp-first-item").hide();

                // Yazılar menüsünden Tüm Yazılar alt sekmesini gizle
                $("#toplevel_page_edit > ul > li.wp-first-item").hide();

                // Yeni oluşturulan ana menülerin alt sekmelerini gizle
                // Kütüphane ana menüsünün alt sekmelerini gizle
                $("a[href=\'upload.php\']").closest("li").find(".wp-submenu").hide();
                $("a[href=\'upload.php\']").closest("li").find("ul").hide();

                // Postlar ana menüsünün alt sekmelerini gizle
                $("a[href=\'edit.php\']").closest("li").find(".wp-submenu").hide();
                $("a[href=\'edit.php\']").closest("li").find("ul").hide();

                // Menü ID\'lerine göre alt sekmeleri gizle
                $("#adminmenu li").each(function() {
                    var $menuItem = $(this);
                    var $link = $menuItem.find("a").first();
                    var href = $link.attr("href") || "";

                    // Kütüphane ana menüsü için
                    if (href === "upload.php") {
                        $menuItem.find(".wp-submenu").hide();
                        $menuItem.find("ul").hide();
                        $menuItem.removeClass("wp-has-submenu");
                    }

                    // Postlar ana menüsü için
                    if (href === "edit.php") {
                        $menuItem.find(".wp-submenu").hide();
                        $menuItem.find("ul").hide();
                        $menuItem.removeClass("wp-has-submenu");
                    }
                });

                // Pazarlama menüsünü gizle
                $("#toplevel_page_woocommerce-marketing").hide();
                $("#toplevel_page_woocommerce-marketing > a").hide();
                $("a[href*=\'woocommerce-marketing\']").parent().hide();
                $("a[href*=\'wc-admin&path=/marketing\']").parent().hide();

                // WooCommerce Raporlar menüsünü gizle
                $("#toplevel_page_admin-page-wc-reports").hide();
                $("#toplevel_page_admin-page-wc-reports > a").hide();
                $("a[href*=\'admin-page-wc-reports\']").parent().hide();

                // WooCommerce Analytics menüsünü gizle
                $("#toplevel_page_wc-admin-path--analytics-overview").hide();
                $("#toplevel_page_wc-admin-path--analytics-overview > a").hide();
                $("a[href*=\'wc-admin&path=/analytics\']").parent().hide();

                // WooCommerce ürün kategorileri ve etiketleri menülerini gizle
                $("a[href*=\'taxonomy=product_cat\']").parent().hide();
                $("a[href*=\'taxonomy=product_tag\']").parent().hide();
                $("#toplevel_page_edit-post_type-product ul li a[href*=\'taxonomy=product_cat\']").parent().hide();
                $("#toplevel_page_edit-post_type-product ul li a[href*=\'taxonomy=product_tag\']").parent().hide();

                // Boşluk oluşturan belirli menü öğelerini gizle
                $("#adminmenu > li:nth-child(12)").hide();
                $("#adminmenu > li:nth-child(10)").hide();
                $("#adminmenu > li:nth-child(8)").hide();
                $("#adminmenu > li:nth-child(3)").hide();

                // Ortamlar menusunu gizle
                $("#menu-media").hide();
                $("#menu-media > a").hide();

                // WooCommerce separator gizle
                $("#adminmenu > li.wp-not-current-submenu.wp-menu-separator.woocommerce").hide();

                // Tutor LMS para çekme menülerini gizle
                $("a[href*=\'tutor-withdrawals\']").parent().hide();
                $("a[href*=\'tutor_withdraw\']").parent().hide();
                $("a[href*=\'withdraw\']").parent().hide();

                // Menü metni ile para çekme, pazarlama ve taksonomi menülerini gizle
                $("#adminmenu a").each(function() {
                    var text = $(this).text().trim();
                    if (text === "Para Çekme Talepleri" ||
                        text === "Withdrawals" ||
                        text === "Para Çekme" ||
                        text === "Withdraw" ||
                        text === "Ödemeler" ||
                        text === "Payments" ||
                        text === "Pazarlama" ||
                        text === "Marketing" ||
                        text === "Kategoriler" ||
                        text === "Categories" ||
                        text === "Etiketler" ||
                        text === "Tags" ||
                        text === "Product categories" ||
                        text === "Product tags") {
                        $(this).parent().hide();
                    }
                });

                // Aktif menü stilini düzelt
                function fixActiveMenuStyling() {
                    var currentUrl = window.location.href;

                    // Siparişler menüsü için
                    if (currentUrl.includes("page=wc-orders")) {
                        $("#adminmenu li").removeClass("current wp-has-current-submenu");
                        var ordersMenu = $("#adminmenu").find("a[href*=\'wc-orders\']").closest("li");
                        ordersMenu.addClass("current wp-has-current-submenu");
                    }

                    // Müşteriler menüsü için
                    if (currentUrl.includes("users.php") && currentUrl.includes("role=customer")) {
                        $("#adminmenu li").removeClass("current wp-has-current-submenu");
                        var customersMenu = $("#adminmenu").find("a[href*=\'role=customer\']").closest("li");
                        customersMenu.addClass("current wp-has-current-submenu");
                    }

                    // Raporlar menüsü için
                    if (currentUrl.includes("page=wc-reports")) {
                        $("#adminmenu li").removeClass("current wp-has-current-submenu");
                        var reportsMenu = $("#adminmenu").find("a[href*=\'wc-reports\']").closest("li");
                        reportsMenu.addClass("current wp-has-current-submenu");
                    }

                    // Kuponlar menüsü için
                    if (currentUrl.includes("post_type=shop_coupon")) {
                        $("#adminmenu li").removeClass("current wp-has-current-submenu");
                        var couponsMenu = $("#adminmenu").find("a[href*=\'shop_coupon\']").closest("li");
                        couponsMenu.addClass("current wp-has-current-submenu");
                    }

                    // Kütüphane menüsü için
                    if (currentUrl.includes("upload.php")) {
                        $("#adminmenu li").removeClass("current wp-has-current-submenu");
                        var libraryMenu = $("#adminmenu").find("a[href=\'upload.php\']").closest("li");
                        libraryMenu.addClass("current wp-has-current-submenu");
                    }

                    // Postlar menüsü için
                    if (currentUrl.includes("edit.php") && !currentUrl.includes("post_type=")) {
                        $("#adminmenu li").removeClass("current wp-has-current-submenu");
                        var postsMenu = $("#adminmenu").find("a[href=\'edit.php\']").closest("li");
                        postsMenu.addClass("current wp-has-current-submenu");
                    }
                }

                // Sayfa yüklendiğinde ve AJAX sonrasında çalıştır
                fixActiveMenuStyling();
                $(document).ajaxComplete(function() {
                    fixActiveMenuStyling();
                });
            });
        </script>';
    }
    
    /**
     * Kullanıcı yetkilerini filtrele
     */
    public function filter_user_capabilities($allcaps, $caps, $args, $user) {
        // Admin kullanıcıları hiçbir zaman etkilenmez
        if (in_array('administrator', $user->roles)) {
            return $allcaps;
        }

        // Sadece Tutor Instructor rolündeki kullanıcılar için
        if (!in_array(self::TUTOR_INSTRUCTOR_ROLE, $user->roles)) {
            return $allcaps;
        }

        // Tutor LMS ve WooCommerce yetkilerini garanti et
        $guaranteed_caps = [
            // Tutor LMS yetkileri - Instructor rolündeki tüm yetkiler
            'edit_posts' => true,
            'edit_published_posts' => true,
            'publish_posts' => true,
            'delete_posts' => true,
            'delete_published_posts' => true,
            'read' => true,
            'upload_files' => true,
            'manage_tutor_instructor' => true,
            'manage_tutor' => true,
            'tutor_instructor' => true,

            // Kurs yetkileri
            'edit_tutor_course' => true,
            'read_tutor_course' => true,
            'delete_tutor_course' => true,
            'delete_tutor_courses' => true,
            'edit_tutor_courses' => true,
            'edit_others_tutor_courses' => true,
            'read_private_tutor_courses' => true,
            'publish_tutor_courses' => true,

            // Ders yetkileri
            'edit_tutor_lesson' => true,
            'read_tutor_lesson' => true,
            'delete_tutor_lesson' => true,
            'delete_tutor_lessons' => true,
            'edit_tutor_lessons' => true,
            'edit_others_tutor_lessons' => true,
            'read_private_tutor_lessons' => true,
            'publish_tutor_lessons' => true,

            // Quiz yetkileri
            'edit_tutor_quiz' => true,
            'read_tutor_quiz' => true,
            'delete_tutor_quiz' => true,
            'delete_tutor_quizzes' => true,
            'edit_tutor_quizzes' => true,
            'edit_others_tutor_quizzes' => true,
            'read_private_tutor_quizzes' => true,
            'publish_tutor_quizzes' => true,

            // Soru yetkileri
            'edit_tutor_question' => true,
            'read_tutor_question' => true,
            'delete_tutor_question' => true,
            'delete_tutor_questions' => true,
            'edit_tutor_questions' => true,
            'edit_others_tutor_questions' => true,
            'publish_tutor_questions' => true,
            'read_private_tutor_questions' => true,

            // Ek okuma yetkileri
            'read_course' => true,
            'read_lesson' => true,
            'read_quiz' => true,
            'read_question' => true,
            'read_announcement' => true,

            // WooCommerce yetkileri - Tam erişim
            'manage_woocommerce' => true,
            'view_woocommerce_reports' => true,
            'edit_shop_orders' => true,
            'edit_others_shop_orders' => true,
            'edit_products' => true,
            'edit_others_products' => true,
            'manage_product_terms' => true,
            'edit_shop_coupons' => true,
            'edit_others_shop_coupons' => true,

            // Değerlendirmeler yetkileri
            'moderate_comments' => true,
            'edit_comment' => true,
            'delete_comment' => true,
        ];

        return array_merge($allcaps, $guaranteed_caps);
    }
    
    /**
     * Admin bildirimleri
     */
    public function admin_notices() {
        // Eklenti etkinleştirildikten sonra başarı mesajı
        if (get_transient('role_custom_activated_notice')) {
            echo '<div class="notice notice-success is-dismissible">';
            echo '<p><strong>' . __('Role Custom:', 'role-custom') . '</strong> ';
            echo __('Eklenti başarıyla etkinleştirildi. Tutor Instructor rolü için WooCommerce yetkileri eklendi.', 'role-custom');
            echo '</p></div>';
            delete_transient('role_custom_activated_notice');
        }

        // Tutor Instructor rolü eksikse hata mesajı
        if (!get_role(self::TUTOR_INSTRUCTOR_ROLE) && current_user_can('manage_options')) {
            echo '<div class="notice notice-error">';
            echo '<p><strong>' . __('Role Custom:', 'role-custom') . '</strong> ';
            echo __('Tutor Instructor rolü bulunamadı! Tutor LMS eklentisini etkinleştirin.', 'role-custom');
            echo ' <a href="' . admin_url('plugins.php') . '">Eklentiler sayfasına git</a>';
            echo '</p></div>';
        }

        // Tutor LMS veya WooCommerce eksikse uyarı
        if (!class_exists('TUTOR\Tutor')) {
            echo '<div class="notice notice-warning">';
            echo '<p><strong>' . __('Role Custom:', 'role-custom') . '</strong> ';
            echo __('Tutor LMS eklentisi bulunamadı. Menü kısıtlamaları çalışmayabilir.', 'role-custom');
            echo '</p></div>';
        }

        if (!class_exists('WooCommerce')) {
            echo '<div class="notice notice-warning">';
            echo '<p><strong>' . __('Role Custom:', 'role-custom') . '</strong> ';
            echo __('WooCommerce eklentisi bulunamadı. WooCommerce yetkileri çalışmayabilir.', 'role-custom');
            echo '</p></div>';
        }
    }



    /**
     * Role Custom admin menüsünü ekle
     */
    public function add_role_custom_admin_menu() {
        // Sadece admin ve yönetici rolleri için menüyü göster
        if (!current_user_can('manage_options')) {
            return;
        }

        // Ana Role Custom menüsü
        add_menu_page(
            __('Role Custom', 'role-custom'),           // Sayfa başlığı
            __('Role Custom', 'role-custom'),           // Menü başlığı
            'manage_options',                           // Yetki
            'role-custom',                              // Menü slug'ı
            [$this, 'display_instructors_page'],       // Callback fonksiyonu (varsayılan olarak eğitmenler sayfası)
            'dashicons-admin-users',                    // İkon
            30                                          // Pozisyon (Kullanıcılar menüsünden sonra)
        );

        // Eğitmenler alt menüsü
        add_submenu_page(
            'role-custom',                              // Ana menü slug'ı
            __('Eğitmenler', 'role-custom'),            // Sayfa başlığı
            __('Eğitmenler', 'role-custom'),            // Menü başlığı
            'manage_options',                           // Yetki
            'role-custom',                              // Menü slug'ı (ana menü ile aynı)
            [$this, 'display_instructors_page']        // Callback fonksiyonu
        );

        // Ayarlar alt menüsü
        add_submenu_page(
            'role-custom',                              // Ana menü slug'ı
            __('Ayarlar', 'role-custom'),               // Sayfa başlığı
            __('Ayarlar', 'role-custom'),               // Menü başlığı
            'manage_options',                           // Yetki
            'role-custom-settings',                     // Menü slug'ı
            [$this, 'display_settings_page']           // Callback fonksiyonu
        );
    }

    /**
     * Eğitmenler sayfasını görüntüle
     */
    public function display_instructors_page() {
        // Tutor Instructor rolündeki kullanıcıları al
        $instructors = get_users([
            'role' => self::TUTOR_INSTRUCTOR_ROLE,
            'orderby' => 'display_name',
            'order' => 'ASC'
        ]);

        // İstatistikleri hesapla
        $total_instructors = count($instructors);
        $total_courses = 0;
        $active_instructors = 0;

        foreach ($instructors as $instructor) {
            $course_count = count_user_posts($instructor->ID, 'courses');
            $total_courses += $course_count;
            if ($course_count > 0) {
                $active_instructors++;
            }
        }

        // Sayfa HTML'ini oluştur
        ?>
        <div class="wrap role-custom-admin-page">
            <div class="role-custom-page-title">
                <span class="dashicons dashicons-admin-users"></span>
                <h1><?php _e('Eğitmenler', 'role-custom'); ?></h1>
            </div>

            <!-- İstatistik Kartları -->
            <div class="role-custom-stats-cards">
                <div class="role-custom-stat-card">
                    <h3><?php _e('Toplam Eğitmen', 'role-custom'); ?></h3>
                    <div class="stat-number"><?php echo esc_html($total_instructors); ?></div>
                    <div class="stat-description"><?php _e('Tutor Instructor rolündeki kullanıcılar', 'role-custom'); ?></div>
                </div>
                <div class="role-custom-stat-card">
                    <h3><?php _e('Aktif Eğitmen', 'role-custom'); ?></h3>
                    <div class="stat-number"><?php echo esc_html($active_instructors); ?></div>
                    <div class="stat-description"><?php _e('En az 1 kursu olan eğitmenler', 'role-custom'); ?></div>
                </div>
                <div class="role-custom-stat-card">
                    <h3><?php _e('Toplam Kurs', 'role-custom'); ?></h3>
                    <div class="stat-number"><?php echo esc_html($total_courses); ?></div>
                    <div class="stat-description"><?php _e('Tüm eğitmenler tarafından oluşturulan kurslar', 'role-custom'); ?></div>
                </div>
            </div>

            <?php if (empty($instructors)): ?>
                <div class="role-custom-empty-state">
                    <span class="dashicons dashicons-admin-users"></span>
                    <h3><?php _e('Henüz Eğitmen Yok', 'role-custom'); ?></h3>
                    <p><?php _e('Henüz hiç Tutor Instructor rolünde kullanıcı bulunmuyor.', 'role-custom'); ?></p>
                </div>
            <?php else: ?>
                <div class="role-custom-instructors-table">
                    <table class="wp-list-table widefat fixed striped users">
                        <thead>
                            <tr>
                                <th scope="col" class="manage-column column-username column-primary">
                                    <?php _e('Kullanıcı Adı', 'role-custom'); ?>
                                </th>
                                <th scope="col" class="manage-column column-name">
                                    <?php _e('Ad Soyad', 'role-custom'); ?>
                                </th>
                                <th scope="col" class="manage-column column-email">
                                    <?php _e('E-posta', 'role-custom'); ?>
                                </th>
                                <th scope="col" class="manage-column column-role">
                                    <?php _e('Rol', 'role-custom'); ?>
                                </th>
                                <th scope="col" class="manage-column column-posts">
                                    <?php _e('Kurs Sayısı', 'role-custom'); ?>
                                </th>
                                <th scope="col" class="manage-column column-date">
                                    <?php _e('Kayıt Tarihi', 'role-custom'); ?>
                                </th>
                                <th scope="col" class="manage-column column-actions">
                                    <?php _e('İşlemler', 'role-custom'); ?>
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($instructors as $instructor): ?>
                                <?php
                                // Kullanıcının kurs sayısını al
                                $course_count = count_user_posts($instructor->ID, 'courses');
                                ?>
                                <tr>
                                    <td class="username column-username column-primary">
                                        <strong>
                                            <a href="<?php echo esc_url(get_edit_user_link($instructor->ID)); ?>">
                                                <?php echo esc_html($instructor->user_login); ?>
                                            </a>
                                        </strong>
                                        <div class="row-actions">
                                            <span class="edit">
                                                <a href="<?php echo esc_url(get_edit_user_link($instructor->ID)); ?>">
                                                    <?php _e('Düzenle', 'role-custom'); ?>
                                                </a>
                                            </span>
                                        </div>
                                    </td>
                                    <td class="name column-name">
                                        <?php echo esc_html($instructor->display_name); ?>
                                    </td>
                                    <td class="email column-email">
                                        <a href="mailto:<?php echo esc_attr($instructor->user_email); ?>">
                                            <?php echo esc_html($instructor->user_email); ?>
                                        </a>
                                    </td>
                                    <td class="role column-role">
                                        <span class="role-custom-status-active">
                                            <?php _e('Tutor Instructor', 'role-custom'); ?>
                                        </span>
                                    </td>
                                    <td class="posts column-posts">
                                        <?php if ($course_count > 0): ?>
                                            <strong><?php echo esc_html($course_count); ?></strong>
                                        <?php else: ?>
                                            <span style="color: #999;">0</span>
                                        <?php endif; ?>
                                    </td>
                                    <td class="date column-date">
                                        <?php echo esc_html(date_i18n(get_option('date_format'), strtotime($instructor->user_registered))); ?>
                                    </td>
                                    <td class="actions column-actions">
                                        <?php if (!in_array('administrator', $instructor->roles)): ?>
                                            <div class="role-custom-action-buttons">
                                                <button type="button" class="button button-secondary role-custom-debug-btn"
                                                        data-user-id="<?php echo esc_attr($instructor->ID); ?>"
                                                        data-user-name="<?php echo esc_attr($instructor->display_name); ?>"
                                                        style="background: #f0ad4e; border-color: #eea236; color: #fff; font-size: 10px; padding: 2px 4px;">
                                                    <?php _e('Debug', 'role-custom'); ?>
                                                </button>
                                                <button type="button" class="button button-secondary role-custom-clean-meta-btn"
                                                        data-user-id="<?php echo esc_attr($instructor->ID); ?>"
                                                        data-user-name="<?php echo esc_attr($instructor->display_name); ?>"
                                                        style="background: #0073aa; border-color: #0073aa; color: #fff; font-size: 10px; padding: 2px 4px;">
                                                    <?php _e('Meta Temizle', 'role-custom'); ?>
                                                </button>
                                                <button type="button" class="button button-secondary role-custom-remove-btn"
                                                        data-user-id="<?php echo esc_attr($instructor->ID); ?>"
                                                        data-user-name="<?php echo esc_attr($instructor->display_name); ?>"
                                                        style="background: #dc3232; border-color: #dc3232; color: #fff; font-size: 10px; padding: 2px 4px;">
                                                    <?php _e('Çıkar', 'role-custom'); ?>
                                                </button>
                                            </div>
                                        <?php else: ?>
                                            <span class="role-custom-admin-badge">
                                                <?php _e('Admin', 'role-custom'); ?>
                                            </span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
        <?php
    }

    /**
     * Ayarlar sayfasını görüntüle (WordPress ayarlar sayfası içeriği)
     */
    public function display_settings_page() {
        // Sadece admin kullanıcılar için
        if (!current_user_can('manage_options')) {
            wp_die(__('Bu sayfaya erişim yetkiniz yok.', 'role-custom'));
        }

        ?>
        <div class="wrap role-custom-settings-page">
            <h1><?php echo esc_html(__('Role Custom Ayarları', 'role-custom')); ?></h1>

            <?php
            // Ayarlar kaydedildi mesajı
            if (isset($_GET['settings-updated'])) {
                add_settings_error('role_custom_messages', 'role_custom_message', __('Ayarlar kaydedildi.', 'role-custom'), 'updated');
            }

            // Hata/başarı mesajlarını göster
            settings_errors('role_custom_messages');
            ?>

            <form action="options.php" method="post">
                <?php
                settings_fields('role_custom_settings_group');
                do_settings_sections('role-custom-settings');
                submit_button(__('Ayarları Kaydet', 'role-custom'));
                ?>
            </form>

            <hr>

            <h2><?php _e('Mevcut Durum', 'role-custom'); ?></h2>
            <?php $this->display_current_status(); ?>
        </div>
        <?php
    }

    /**
     * Admin script ve style dosyalarını yükle
     */
    public function enqueue_admin_scripts($hook) {
        // Role Custom admin sayfalarında veya ayarlar sayfasında yükle
        if (strpos($hook, 'role-custom') === false && $hook !== 'settings_page_role-custom-settings') {
            return;
        }

        // Ayarlar sayfası için özel CSS
        if ($hook === 'settings_page_role-custom-settings') {
            wp_enqueue_style(
                'role-custom-settings',
                ROLE_CUSTOM_PLUGIN_URL . 'assets/css/admin-settings.css',
                [],
                ROLE_CUSTOM_VERSION
            );
            return; // Ayarlar sayfasında sadece settings CSS'i yükle
        }

        // Admin CSS dosyasını yükle
        wp_enqueue_style(
            'role-custom-admin',
            ROLE_CUSTOM_PLUGIN_URL . 'assets/css/admin.css',
            [],
            ROLE_CUSTOM_VERSION
        );

        // Admin JavaScript dosyasını yükle
        wp_enqueue_script(
            'role-custom-admin',
            ROLE_CUSTOM_PLUGIN_URL . 'assets/js/admin.js',
            ['jquery'],
            ROLE_CUSTOM_VERSION,
            true
        );

        // AJAX için nonce ve URL'leri JavaScript'e gönder
        wp_localize_script('role-custom-admin', 'roleCustomAjax', [
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('role_custom_nonce'),
            'messages' => [
                'success' => __('İşlem başarıyla tamamlandı.', 'role-custom'),
                'error' => __('Bir hata oluştu. Lütfen tekrar deneyin.', 'role-custom'),
                'loading' => __('İşlem yapılıyor...', 'role-custom')
            ]
        ]);

        // WordPress admin stilleri
        wp_enqueue_style('wp-admin');
        wp_enqueue_style('common');
        wp_enqueue_style('forms');
        wp_enqueue_style('dashboard');
    }

    /**
     * AJAX: Eğitmeni çıkar (Meta verileri sil + Abone rolüne çek)
     */
    public function ajax_remove_instructor() {
        // Nonce kontrolü
        if (!wp_verify_nonce($_POST['nonce'], 'role_custom_nonce')) {
            wp_die(__('Güvenlik kontrolü başarısız.', 'role-custom'));
        }

        // Yetki kontrolü
        if (!current_user_can('manage_options')) {
            wp_die(__('Bu işlem için yetkiniz yok.', 'role-custom'));
        }

        $user_id = intval($_POST['user_id']);

        if (!$user_id) {
            wp_send_json_error(__('Geçersiz kullanıcı ID.', 'role-custom'));
        }

        // Kullanıcıyı al
        $user = get_user_by('ID', $user_id);
        if (!$user) {
            wp_send_json_error(__('Kullanıcı bulunamadı.', 'role-custom'));
        }

        // Admin kullanıcıları çıkarılamaz
        if (in_array('administrator', $user->roles)) {
            wp_send_json_error(__('Admin kullanıcıları çıkarılamaz.', 'role-custom'));
        }

        // ADIM 1: Önce eğitmen meta verilerini temizle (basit yöntem)
        $meta_result = $this->clean_instructor_meta_data_simple($user_id);

        if (!$meta_result['success']) {
            wp_send_json_error(__('Meta veri temizleme işlemi başarısız oldu.', 'role-custom'));
        }

        // ADIM 2: Sonra kullanıcıyı abone rolüne çek
        $user->set_role('subscriber');

        // Başarı mesajı
        wp_send_json_success([
            'message' => sprintf(
                __('%s kullanıcısı başarıyla çıkarıldı. %d adet meta veri silindi ve kullanıcı abone rolüne çekildi.', 'role-custom'),
                $user->display_name,
                $meta_result['deleted_count']
            ),
            'deleted_count' => $meta_result['deleted_count'],
            'deleted_keys' => $meta_result['deleted_keys'],
            'tutor_utils_used' => $meta_result['tutor_utils_used'],
            'new_role' => 'subscriber'
        ]);
    }

    /**
     * AJAX: Sadece meta verileri temizle (rol değiştirme)
     */
    public function ajax_clean_meta_only() {
        // Nonce kontrolü
        if (!wp_verify_nonce($_POST['nonce'], 'role_custom_nonce')) {
            wp_die(__('Güvenlik kontrolü başarısız.', 'role-custom'));
        }

        // Yetki kontrolü
        if (!current_user_can('manage_options')) {
            wp_die(__('Bu işlem için yetkiniz yok.', 'role-custom'));
        }

        $user_id = intval($_POST['user_id']);

        if (!$user_id) {
            wp_send_json_error(__('Geçersiz kullanıcı ID.', 'role-custom'));
        }

        // Kullanıcıyı al
        $user = get_user_by('ID', $user_id);
        if (!$user) {
            wp_send_json_error(__('Kullanıcı bulunamadı.', 'role-custom'));
        }

        // Admin kullanıcıları işlem göremez
        if (in_array('administrator', $user->roles)) {
            wp_send_json_error(__('Admin kullanıcıları işlem göremez.', 'role-custom'));
        }

        // Sadece meta verileri temizle (rol değiştirme yok)
        $result = $this->clean_instructor_meta_data_simple($user_id);

        if ($result['success']) {
            wp_send_json_success([
                'message' => sprintf(__('%s kullanıcısının %d adet eğitmen meta verisi silindi.', 'role-custom'), $user->display_name, $result['deleted_count']),
                'deleted_count' => $result['deleted_count'],
                'deleted_keys' => $result['deleted_keys'],
                'tutor_utils_used' => $result['tutor_utils_used']
            ]);
        } else {
            wp_send_json_error(__('Meta veri temizleme başarısız.', 'role-custom'));
        }
    }

    /**
     * AJAX: Debug - Kullanıcının meta verilerini listele
     */
    public function ajax_debug_user_meta() {
        // Nonce kontrolü
        if (!wp_verify_nonce($_POST['nonce'], 'role_custom_nonce')) {
            wp_die(__('Güvenlik kontrolü başarısız.', 'role-custom'));
        }

        // Yetki kontrolü
        if (!current_user_can('manage_options')) {
            wp_die(__('Bu işlem için yetkiniz yok.', 'role-custom'));
        }

        $user_id = intval($_POST['user_id']);

        if (!$user_id) {
            wp_send_json_error(__('Geçersiz kullanıcı ID.', 'role-custom'));
        }

        // Kullanıcıyı al
        $user = get_user_by('ID', $user_id);
        if (!$user) {
            wp_send_json_error(__('Kullanıcı bulunamadı.', 'role-custom'));
        }

        // Tüm meta verileri al
        global $wpdb;
        $all_meta = $wpdb->get_results(
            $wpdb->prepare(
                "SELECT meta_key, meta_value FROM {$wpdb->usermeta} WHERE user_id = %d ORDER BY meta_key",
                $user_id
            )
        );

        // Tutor ile ilgili meta verileri filtrele
        $tutor_meta = [];
        $other_meta = [];

        foreach ($all_meta as $meta) {
            if (
                strpos($meta->meta_key, 'tutor') !== false ||
                strpos($meta->meta_key, 'instructor') !== false ||
                $meta->meta_key === '_is_tutor_instructor'
            ) {
                $tutor_meta[] = [
                    'key' => $meta->meta_key,
                    'value' => $meta->meta_value
                ];
            } else {
                $other_meta[] = [
                    'key' => $meta->meta_key,
                    'value' => $meta->meta_value
                ];
            }
        }

        wp_send_json_success([
            'user_name' => $user->display_name,
            'user_roles' => $user->roles,
            'total_meta' => count($all_meta),
            'tutor_meta_count' => count($tutor_meta),
            'tutor_meta' => $tutor_meta,
            'other_meta_count' => count($other_meta),
            'tutor_utils_available' => function_exists('tutor_utils')
        ]);
    }

    /**
     * Basit meta veri temizleme - Modal'daki gibi çalışan versiyon
     */
    private function clean_instructor_meta_data_simple($user_id) {
        global $wpdb;

        $deleted_count = 0;
        $deleted_keys = [];

        // Önce mevcut tutor meta verilerini bul
        $existing_meta = $wpdb->get_results(
            $wpdb->prepare(
                "SELECT meta_key FROM {$wpdb->usermeta}
                 WHERE user_id = %d AND (
                     meta_key LIKE 'tutor%%' OR
                     meta_key LIKE '_tutor%%' OR
                     meta_key LIKE '%%instructor%%' OR
                     meta_key = '_is_tutor_instructor'
                 )",
                $user_id
            )
        );

        // Her meta veriyi tek tek sil
        foreach ($existing_meta as $meta) {
            if (delete_user_meta($user_id, $meta->meta_key)) {
                $deleted_count++;
                $deleted_keys[] = $meta->meta_key;
            }
        }

        // Tutor Utils kullan
        $tutor_utils_used = false;
        if (function_exists('tutor_utils')) {
            try {
                tutor_utils()->remove_instructor_role($user_id);
                $tutor_utils_used = true;
                $deleted_keys[] = 'tutor_utils()->remove_instructor_role() çalıştırıldı';
            } catch (Exception $e) {
                error_log('Role Custom: tutor_utils() hatası: ' . $e->getMessage());
            }
        }

        return [
            'success' => true,
            'deleted_count' => $deleted_count,
            'deleted_keys' => $deleted_keys,
            'tutor_utils_used' => $tutor_utils_used
        ];
    }

    /**
     * 17 Kurs istatistikleri eklentisindeki başarılı meta veri temizleme mantığı
     * Tutor Utils remove_instructor_role metodunu kullanır
     */
    private function clean_instructor_meta_data_comprehensive($user_id) {
        // Kullanıcı ID'si geçerli mi kontrol et
        if (!$user_id || !get_userdata($user_id)) {
            return [
                'success' => false,
                'message' => __('Geçersiz kullanıcı ID.', 'role-custom')
            ];
        }

        $deleted_count = 0;
        $deleted_keys = [];
        $tutor_utils_used = false;

        // Debug: Önce mevcut meta verileri listele
        global $wpdb;
        $all_user_meta = $wpdb->get_results(
            $wpdb->prepare(
                "SELECT meta_key, meta_value FROM {$wpdb->usermeta} WHERE user_id = %d ORDER BY meta_key",
                $user_id
            )
        );

        // Tutor ile ilgili meta verileri filtrele
        $tutor_meta_before = [];
        foreach ($all_user_meta as $meta) {
            if (
                strpos($meta->meta_key, 'tutor') !== false ||
                strpos($meta->meta_key, 'instructor') !== false ||
                $meta->meta_key === '_is_tutor_instructor'
            ) {
                $tutor_meta_before[] = $meta->meta_key;
            }
        }

        // 1. Tutor LMS'in kendi remove_instructor_role metodunu kullan
        if (function_exists('tutor_utils')) {
            try {
                tutor_utils()->remove_instructor_role($user_id);
                $tutor_utils_used = true;
                $deleted_keys[] = 'tutor_utils()->remove_instructor_role() çalıştırıldı';
            } catch (Exception $e) {
                error_log('Role Custom: tutor_utils()->remove_instructor_role() hatası: ' . $e->getMessage());
            }
        }

        // 2. 17 eklentisindeki gibi kapsamlı manuel temizleme
        $meta_keys_to_delete = [
            '_is_tutor_instructor',
            '_tutor_instructor_status',
            '_tutor_instructor_approved',
            'tutor_profile_photo',
            'tutor_profile_bio',
            'tutor_profile_job_title',
            'tutor_profile_facebook',
            'tutor_profile_twitter',
            'tutor_profile_linkedin',
            'tutor_profile_website',
            'tutor_profile_github',
            '_tutor_instructor_signature',
            '_tutor_instructor_rating',
            '_tutor_instructor_course_count',
            '_tutor_instructor_student_count',
            '_tutor_instructor_earnings',
            'tutor_instructor_enable_qa_for_this_course',
            'tutor_instructor_enable_qa_for_course',
            '_tutor_instructor_course_settings',
            'tutor_instructor_bio',
            'tutor_instructor_designation',
            '_tutor_profile_completed',
            '_tutor_instructor_status'
        ];

        // Her meta veriyi tek tek sil
        foreach ($meta_keys_to_delete as $meta_key) {
            $result = delete_user_meta($user_id, $meta_key);
            if ($result) {
                $deleted_count++;
                $deleted_keys[] = $meta_key;
            }
        }

        // 3. Wildcard ile tutor ile başlayan tüm meta verileri sil
        $wildcard_patterns = [
            'tutor_%',
            '_tutor_%',
            '%instructor%'
        ];

        foreach ($wildcard_patterns as $pattern) {
            $bulk_deleted = $wpdb->query(
                $wpdb->prepare(
                    "DELETE FROM {$wpdb->usermeta} WHERE user_id = %d AND meta_key LIKE %s",
                    $user_id,
                    $pattern
                )
            );

            if ($bulk_deleted > 0) {
                $deleted_count += $bulk_deleted;
                $deleted_keys[] = "Wildcard '{$pattern}' ile {$bulk_deleted} meta veri silindi";
            }
        }

        // 4. Kullanıcının rollerini kontrol et ve tutor_instructor rolünü kaldır
        $user = new WP_User($user_id);

        // Tutor'ın instructor_role değerini kullan
        if (function_exists('tutor') && isset(tutor()->instructor_role)) {
            $instructor_role = tutor()->instructor_role;
            if (in_array($instructor_role, $user->roles)) {
                $user->remove_role($instructor_role);
                $deleted_keys[] = "Rol kaldırıldı: {$instructor_role}";
            }
        }

        // Fallback olarak tutor_instructor rolünü kaldır
        if (in_array('tutor_instructor', $user->roles)) {
            $user->remove_role('tutor_instructor');
            $deleted_keys[] = 'Rol kaldırıldı: tutor_instructor';
        }

        // 5. Son kontrol: Kalan tutor meta verileri var mı?
        $remaining_meta = $wpdb->get_results(
            $wpdb->prepare(
                "SELECT meta_key FROM {$wpdb->usermeta}
                 WHERE user_id = %d AND (
                     meta_key LIKE %s OR
                     meta_key LIKE %s OR
                     meta_key LIKE %s OR
                     meta_key = %s
                 )",
                $user_id,
                'tutor_%',
                '_tutor_%',
                '%instructor%',
                '_is_tutor_instructor'
            )
        );

        if (!empty($remaining_meta)) {
            foreach ($remaining_meta as $meta) {
                delete_user_meta($user_id, $meta->meta_key);
                $deleted_count++;
                $deleted_keys[] = "Kalan meta silindi: {$meta->meta_key}";
            }
        }

        return [
            'success' => true,
            'deleted_count' => $deleted_count,
            'deleted_keys' => $deleted_keys,
            'total_found' => count($tutor_meta_before),
            'tutor_utils_used' => $tutor_utils_used,
            'meta_before' => $tutor_meta_before
        ];
    }

    /**
     * Global erişim için eğitmen meta verilerini temizleyen public fonksiyon
     * Modal'daki gibi çalışan basit versiyon
     */
    public function clean_instructor_meta_data($user_id) {
        return $this->clean_instructor_meta_data_simple($user_id);
    }



    /**
     * Dil dosyalarını yükle
     */
    public function load_textdomain() {
        load_plugin_textdomain(
            'role-custom',
            false,
            dirname(ROLE_CUSTOM_PLUGIN_BASENAME) . '/languages'
        );
    }

    /**
     * Ürün arama sonuçlarını tutor instructor kullanıcıları için filtrele
     */
    public function filter_product_search_results($custom_results, $term, $type, $include_variations, $all_statuses, $limit) {
        // Sadece admin panelinde çalışsın
        if (!is_admin()) {
            return $custom_results;
        }

        // Sadece tutor instructor rolündeki kullanıcılar için
        if (!$this->is_current_user_tutor_instructor()) {
            return $custom_results;
        }

        // Mevcut kullanıcı ID'sini al
        $current_user_id = get_current_user_id();

        // Kullanıcının ürünlerini al
        $user_products = $this->get_user_product_ids($current_user_id);

        if (empty($user_products)) {
            // Kullanıcının hiç ürünü yoksa boş array döndür
            return array();
        }

        // Normal arama işlemini yap ama sadece kullanıcının ürünleri arasında
        global $wpdb;

        $post_types = $include_variations ? array('product', 'product_variation') : array('product');
        $join_query = '';
        $type_where = '';
        $status_where = '';
        $limit_query = '';

        // Variations için parent meta table join
        if ($include_variations) {
            $join_query = " LEFT JOIN {$wpdb->wc_product_meta_lookup} parent_wc_product_meta_lookup
             ON posts.post_type = 'product_variation' AND parent_wc_product_meta_lookup.product_id = posts.post_parent ";
        }

        $post_statuses = current_user_can('edit_private_products') ? array('private', 'publish') : array('publish');

        // OR anahtar kelimelerini kontrol et
        if (stristr($term, ' or ')) {
            $term_groups = preg_split('/\s+or\s+/i', $term);
        } else {
            $term_groups = array($term);
        }

        $search_where = '';
        $search_queries = array();

        foreach ($term_groups as $term_group) {
            $search_terms = array_map('trim', explode(' ', $term_group));
            $search_terms = array_filter($search_terms);

            $term_group_query = array();

            foreach ($search_terms as $search_term) {
                $like = '%' . $wpdb->esc_like($search_term) . '%';

                $term_query = $wpdb->prepare(
                    '( posts.post_title LIKE %s ) OR ( posts.post_excerpt LIKE %s ) OR ( posts.post_content LIKE %s ) OR ( wc_product_meta_lookup.sku LIKE %s )',
                    array_fill(0, 4, $like)
                );

                // Variations için parent meta table kontrolü
                if ($include_variations) {
                    $term_query .= $wpdb->prepare(" OR ( wc_product_meta_lookup.sku = '' AND parent_wc_product_meta_lookup.sku LIKE %s )", $like);
                }

                $term_group_query[] = "( {$term_query} )";
            }

            if (!empty($term_group_query)) {
                $search_queries[] = '( ' . implode(' AND ', $term_group_query) . ' )';
            }
        }

        if (!empty($search_queries)) {
            $search_where = ' AND ( ' . implode(' OR ', $search_queries) . ' )';
        }

        // Post type kontrolü
        if (!empty($type)) {
            $type_where = " AND ( wc_product_meta_lookup.type = '" . esc_sql($type) . "' )";
        }

        // Status kontrolü
        if (!$all_statuses) {
            $status_where = " AND posts.post_status IN ('" . implode("','", array_map('esc_sql', $post_statuses)) . "')";
        }

        // Limit kontrolü
        if ($limit) {
            $limit_query = $wpdb->prepare(' LIMIT %d', $limit);
        }

        // Kullanıcının ürünleri arasında kısıtla
        $user_products_in = implode(',', array_map('intval', $user_products));

        $search_results = $wpdb->get_col(
            "SELECT DISTINCT posts.ID FROM {$wpdb->posts} posts
            LEFT JOIN {$wpdb->wc_product_meta_lookup} wc_product_meta_lookup ON posts.ID = wc_product_meta_lookup.product_id
            {$join_query}
            WHERE posts.post_type IN ('" . implode("','", array_map('esc_sql', $post_types)) . "')
            {$search_where}
            {$type_where}
            {$status_where}
            AND posts.ID IN ({$user_products_in})
            ORDER BY posts.post_parent ASC, posts.post_title ASC
            {$limit_query}"
        );

        return array_map('intval', $search_results);
    }

    /**
     * Sipariş arama sonuçlarını tutor instructor kullanıcıları için filtrele
     */
    public function filter_order_search_results($order_ids, $term, $search_fields) {
        // Sadece admin panelinde çalışsın
        if (!is_admin()) {
            return $order_ids;
        }

        // Sadece tutor instructor rolündeki kullanıcılar için
        if (!$this->is_current_user_tutor_instructor()) {
            return $order_ids;
        }

        // Mevcut kullanıcı ID'sini al
        $current_user_id = get_current_user_id();

        // Kullanıcının ürünlerini al
        $user_products = $this->get_user_product_ids($current_user_id);

        if (empty($user_products)) {
            // Kullanıcının hiç ürünü yoksa boş array döndür
            return array();
        }

        // Kullanıcının ürünlerini içeren siparişleri bul
        $user_order_ids = $this->get_orders_with_user_products($user_products);

        if (empty($user_order_ids)) {
            return array();
        }

        // Arama sonuçlarını kullanıcının siparişleriyle kesişim al
        if (empty($order_ids)) {
            return $user_order_ids;
        }

        return array_intersect($order_ids, $user_order_ids);
    }

    /**
     * HPOS sipariş arama sonuçlarını tutor instructor kullanıcıları için filtrele
     */
    public function filter_hpos_order_search_results($order_ids, $term) {
        // Sadece admin panelinde çalışsın
        if (!is_admin()) {
            return $order_ids;
        }

        // Sadece tutor instructor rolündeki kullanıcılar için
        if (!$this->is_current_user_tutor_instructor()) {
            return $order_ids;
        }

        // Mevcut kullanıcı ID'sini al
        $current_user_id = get_current_user_id();

        // Kullanıcının ürünlerini al
        $user_products = $this->get_user_product_ids($current_user_id);

        if (empty($user_products)) {
            // Kullanıcının hiç ürünü yoksa boş array döndür
            return array();
        }

        // Kullanıcının ürünlerini içeren siparişleri bul
        $user_order_ids = $this->get_orders_with_user_products($user_products);

        if (empty($user_order_ids)) {
            return array();
        }

        // Arama sonuçlarını kullanıcının siparişleriyle kesişim al
        if (empty($order_ids)) {
            return $user_order_ids;
        }

        return array_intersect($order_ids, $user_order_ids);
    }

    /**
     * Kupon arama sonuçlarını tutor instructor kullanıcıları için filtrele
     */
    public function filter_coupon_search_results($where, $query) {
        global $wpdb;

        // Sadece admin panelinde çalışsın
        if (!is_admin()) {
            return $where;
        }

        // Sadece tutor instructor rolündeki kullanıcılar için
        if (!$this->is_current_user_tutor_instructor()) {
            return $where;
        }

        // Ana sorgu değilse çık
        if (!$query->is_main_query()) {
            return $where;
        }

        // Kupon arama kontrolü
        $post_type = $query->get('post_type');
        if ($post_type !== 'shop_coupon') {
            return $where;
        }

        // Arama terimi var mı kontrol et
        $search_term = $query->get('s');
        if (empty($search_term)) {
            return $where;
        }

        // Mevcut kullanıcı ID'sini al
        $current_user_id = get_current_user_id();

        // Kullanıcının kuponlarını kısıtla
        $where .= $wpdb->prepare(" AND {$wpdb->posts}.post_author = %d", $current_user_id);

        return $where;
    }

    /**
     * WooCommerce verilerini tutor instructor kullanıcıları için filtrele
     */
    public function filter_woocommerce_data_for_tutor_instructor($query) {
        // Sadece admin panelinde çalışsın
        if (!is_admin()) {
            return;
        }

        // Sadece tutor instructor rolündeki kullanıcılar için
        if (!$this->is_current_user_tutor_instructor()) {
            return;
        }

        // Ana sorgu değilse çık
        if (!$query->is_main_query()) {
            return;
        }

        // Mevcut kullanıcı ID'sini al
        $current_user_id = get_current_user_id();

        // Ürünler sayfası için filtreleme - Sadece liste görünümünde
        if ($query->get('post_type') === 'product') {
            // Sadece edit.php sayfasında (liste görünümü) filtreleme yap
            global $pagenow;
            if ($pagenow === 'edit.php' && !isset($_GET['action'])) {
                $query->set('author', $current_user_id);
            }
        }

        // Kuponlar sayfası için filtreleme
        if ($query->get('post_type') === 'shop_coupon') {
            $query->set('author', $current_user_id);
        }

        // Postlar sayfası için filtreleme - Sadece liste görünümünde
        if ($query->get('post_type') === 'post') {
            // Sadece edit.php sayfasında (liste görünümü) filtreleme yap
            global $pagenow;
            if ($pagenow === 'edit.php' && !isset($_GET['action'])) {
                $query->set('author', $current_user_id);
            }
        }
    }

    /**
     * Siparişleri kullanıcının ürünlerine göre filtrele
     */
    public function filter_orders_by_user_products($where, $query) {
        global $wpdb;

        // Sadece admin panelinde çalışsın
        if (!is_admin()) {
            return $where;
        }

        // Sadece tutor instructor rolündeki kullanıcılar için
        if (!$this->is_current_user_tutor_instructor()) {
            return $where;
        }

        // Ana sorgu değilse çık
        if (!$query->is_main_query()) {
            return $where;
        }

        // Siparişler sayfası kontrolü - daha geniş kontrol
        $post_type = $query->get('post_type');
        if (!in_array($post_type, ['shop_order', 'wc_order']) && !$this->is_orders_page()) {
            return $where;
        }

        // Mevcut kullanıcı ID'sini al
        $current_user_id = get_current_user_id();

        // Kullanıcının ürünlerini al
        $user_products = $this->get_user_product_ids($current_user_id);

        if (empty($user_products)) {
            // Kullanıcının hiç ürünü yoksa hiçbir sipariş gösterme
            $where .= " AND 1=0";
            return $where;
        }

        // Ürün ID'lerini string olarak hazırla
        $product_ids = implode(',', array_map('intval', $user_products));

        // Siparişleri kullanıcının ürünlerine göre filtrele
        $order_filter = " AND {$wpdb->posts}.ID IN (
            SELECT DISTINCT order_items.order_id
            FROM {$wpdb->prefix}woocommerce_order_items as order_items
            INNER JOIN {$wpdb->prefix}woocommerce_order_itemmeta as order_item_meta
                ON order_items.order_item_id = order_item_meta.order_item_id
            WHERE order_item_meta.meta_key = '_product_id'
                AND order_item_meta.meta_value IN ({$product_ids})
        )";

        $where .= $order_filter;

        return $where;
    }

    /**
     * HPOS siparişlerini kullanıcının ürünlerine göre filtrele
     */
    public function filter_hpos_orders_by_user_products($clauses, $query) {
        global $wpdb;

        // Sadece admin panelinde çalışsın
        if (!is_admin()) {
            return $clauses;
        }

        // Sadece tutor instructor rolündeki kullanıcılar için
        if (!$this->is_current_user_tutor_instructor()) {
            return $clauses;
        }

        // Mevcut kullanıcı ID'sini al
        $current_user_id = get_current_user_id();

        // Kullanıcının ürünlerini al
        $user_products = $this->get_user_product_ids($current_user_id);

        if (empty($user_products)) {
            // Kullanıcının hiç ürünü yoksa hiçbir sipariş gösterme
            $clauses['where'] .= " AND 1=0";
            return $clauses;
        }

        // Ürün ID'lerini string olarak hazırla
        $product_ids = implode(',', array_map('intval', $user_products));

        // HPOS tabloları için sipariş filtreleme - Doğru tablo adı
        $orders_table = $wpdb->prefix . 'wc_orders';
        $hpos_filter = " AND {$orders_table}.id IN (
            SELECT DISTINCT order_items.order_id
            FROM {$wpdb->prefix}woocommerce_order_items as order_items
            INNER JOIN {$wpdb->prefix}woocommerce_order_itemmeta as order_item_meta
                ON order_items.order_item_id = order_item_meta.order_item_id
            WHERE order_item_meta.meta_key = '_product_id'
                AND order_item_meta.meta_value IN ({$product_ids})
        )";

        $clauses['where'] .= $hpos_filter;

        return $clauses;
    }

    /**
     * WooCommerce admin sayfalarında ek filtreleme ekle
     */
    public function add_woocommerce_admin_filters() {
        // Sadece tutor instructor rolündeki kullanıcılar için
        if (!$this->is_current_user_tutor_instructor()) {
            return;
        }

        global $typenow;



        // Ürünler sayfası için ek filtreleme
        if ($typenow === 'product') {
            add_filter('views_edit-product', [$this, 'filter_product_views']);
        }

        // Kuponlar sayfası için ek filtreleme
        if ($typenow === 'shop_coupon') {
            add_filter('views_edit-shop_coupon', [$this, 'filter_coupon_views']);
        }

        // Siparişler sayfası için ek filtreleme - daha geniş kontrol
        if ($typenow === 'shop_order' || $typenow === 'wc_order' || $this->is_orders_page()) {
            add_filter('views_edit-shop_order', [$this, 'filter_order_views']);
        }

        // Postlar sayfası için ek filtreleme
        if ($typenow === 'post') {
            add_filter('views_edit-post', [$this, 'filter_post_views']);
            // Post düzenleme yetkilerini kontrol et
            add_filter('map_meta_cap', [$this, 'allow_instructor_edit_own_posts'], 10, 4);
        }
    }

    /**
     * WooCommerce ürün row actions'larını düzelt
     * Tutor Instructor kullanıcıları için düzenle ve hızlı düzenle seçeneklerini geri ekle
     */
    public function fix_product_row_actions($actions, $post) {
        // Sadece tutor instructor rolündeki kullanıcılar için
        if (!$this->is_current_user_tutor_instructor()) {
            return $actions;
        }

        // Sadece ürünler için
        if ($post->post_type !== 'product') {
            return $actions;
        }

        // Sadece admin panelinde
        if (!is_admin()) {
            return $actions;
        }

        // Tutor Instructor kullanıcıları için tüm ürün row actions'larını yeniden oluştur
        $new_actions = array();

        // Düzenle linki - her zaman ekle
        $new_actions['edit'] = sprintf(
            '<a href="%s" aria-label="%s">%s</a>',
            get_edit_post_link($post->ID),
            /* translators: %s: Post title. */
            esc_attr(sprintf(__('Edit &#8220;%s&#8221;'), $post->post_title)),
            __('Edit')
        );

        // Hızlı düzenle linki - her zaman ekle
        $new_actions['inline hide-if-no-js'] = sprintf(
            '<button type="button" class="button-link editinline" aria-label="%s" aria-expanded="false">%s</button>',
            /* translators: %s: Post title. */
            esc_attr(sprintf(__('Quick edit &#8220;%s&#8221; inline'), $post->post_title)),
            __('Quick Edit')
        );

        // Çöp kutusuna taşı/geri yükle linki
        if ($post->post_status === 'trash') {
            $new_actions['untrash'] = sprintf(
                '<a href="%s" aria-label="%s">%s</a>',
                wp_nonce_url(admin_url('edit.php?post_type=product&action=untrash&post=' . $post->ID), 'untrash-post_' . $post->ID),
                /* translators: %s: Post title. */
                esc_attr(sprintf(__('Restore &#8220;%s&#8221; from the Trash'), $post->post_title)),
                __('Restore')
            );

            $new_actions['delete'] = sprintf(
                '<a href="%s" class="submitdelete" aria-label="%s">%s</a>',
                wp_nonce_url(admin_url('edit.php?post_type=product&action=delete&post=' . $post->ID), 'delete-post_' . $post->ID),
                /* translators: %s: Post title. */
                esc_attr(sprintf(__('Delete &#8220;%s&#8221; permanently'), $post->post_title)),
                __('Delete Permanently')
            );
        } else {
            $new_actions['trash'] = sprintf(
                '<a href="%s" class="submitdelete" aria-label="%s">%s</a>',
                get_delete_post_link($post->ID),
                /* translators: %s: Post title. */
                esc_attr(sprintf(__('Move &#8220;%s&#8221; to the Trash'), $post->post_title)),
                __('Trash')
            );
        }

        // Görüntüle linki
        if ($post->post_status === 'publish') {
            $new_actions['view'] = sprintf(
                '<a href="%s" rel="bookmark" aria-label="%s">%s</a>',
                get_permalink($post->ID),
                /* translators: %s: Post title. */
                esc_attr(sprintf(__('View &#8220;%s&#8221;'), $post->post_title)),
                __('View')
            );
        }

        // Önizleme linki
        if (in_array($post->post_status, array('pending', 'draft', 'future'))) {
            $preview_link = get_preview_post_link($post);
            if ($preview_link) {
                $new_actions['view'] = sprintf(
                    '<a href="%s" rel="bookmark" aria-label="%s">%s</a>',
                    esc_url($preview_link),
                    /* translators: %s: Post title. */
                    esc_attr(sprintf(__('Preview &#8220;%s&#8221;'), $post->post_title)),
                    __('Preview')
                );
            }
        }

        // Mevcut actions'ları yeni actions ile değiştir
        return $new_actions;
    }

    /**
     * Ürün düzenleme yetkilerini düzelt
     * Tutor Instructor kullanıcıları için ürün düzenleme yetkilerini güçlendir
     */
    public function fix_product_edit_capabilities($caps, $cap, $user_id, $args) {
        $user = get_userdata($user_id);
        if (!$user) {
            return $caps;
        }

        // Admin kullanıcıları hiçbir zaman etkilenmez
        if (in_array('administrator', $user->roles)) {
            return $caps;
        }

        // Sadece tutor instructor rolündeki kullanıcılar için
        if (!in_array(self::TUTOR_INSTRUCTOR_ROLE, $user->roles)) {
            return $caps;
        }

        // Ürün ile ilgili tüm yetkileri bypass et
        $product_caps = [
            'edit_post', 'delete_post', 'read_post', 'publish_post',
            'edit_products', 'edit_others_products', 'edit_private_products', 'edit_published_products',
            'delete_products', 'delete_others_products', 'delete_private_products', 'delete_published_products',
            'read_products', 'read_private_products',
            'publish_products'
        ];

        if (in_array($cap, $product_caps)) {
            // Post ID'si varsa ve ürünse
            if (isset($args[0])) {
                $post_id = $args[0];
                $post = get_post($post_id);

                if ($post && $post->post_type === 'product') {
                    // Tutor Instructor kullanıcıları tüm ürünleri düzenleyebilir
                    return array('exist');
                }
            } else {
                // Genel ürün yetkileri için
                return array('exist');
            }
        }

        return $caps;
    }

    /**
     * WooCommerce kupon row actions'larını düzelt
     * Tutor Instructor kullanıcıları için düzenle ve hızlı düzenle seçeneklerini geri ekle
     */
    public function fix_coupon_row_actions($actions, $post) {
        // Sadece tutor instructor rolündeki kullanıcılar için
        if (!$this->is_current_user_tutor_instructor()) {
            return $actions;
        }

        // Sadece kuponlar için
        if ($post->post_type !== 'shop_coupon') {
            return $actions;
        }

        // Sadece admin panelinde
        if (!is_admin()) {
            return $actions;
        }

        // Tutor Instructor kullanıcıları için tüm kupon row actions'larını yeniden oluştur
        $new_actions = array();

        // Düzenle linki - her zaman ekle
        $new_actions['edit'] = sprintf(
            '<a href="%s" aria-label="%s">%s</a>',
            get_edit_post_link($post->ID),
            /* translators: %s: Post title. */
            esc_attr(sprintf(__('Edit &#8220;%s&#8221;'), $post->post_title)),
            __('Edit')
        );

        // Hızlı düzenle linki - her zaman ekle
        $new_actions['inline hide-if-no-js'] = sprintf(
            '<button type="button" class="button-link editinline" aria-label="%s" aria-expanded="false">%s</button>',
            /* translators: %s: Post title. */
            esc_attr(sprintf(__('Quick edit &#8220;%s&#8221; inline'), $post->post_title)),
            __('Quick Edit')
        );

        // Çöp kutusuna taşı/geri yükle linki
        if ($post->post_status === 'trash') {
            $new_actions['untrash'] = sprintf(
                '<a href="%s" aria-label="%s">%s</a>',
                wp_nonce_url(admin_url('edit.php?post_type=shop_coupon&action=untrash&post=' . $post->ID), 'untrash-post_' . $post->ID),
                /* translators: %s: Post title. */
                esc_attr(sprintf(__('Restore &#8220;%s&#8221; from the Trash'), $post->post_title)),
                __('Restore')
            );

            $new_actions['delete'] = sprintf(
                '<a href="%s" class="submitdelete" aria-label="%s">%s</a>',
                wp_nonce_url(admin_url('edit.php?post_type=shop_coupon&action=delete&post=' . $post->ID), 'delete-post_' . $post->ID),
                /* translators: %s: Post title. */
                esc_attr(sprintf(__('Delete &#8220;%s&#8221; permanently'), $post->post_title)),
                __('Delete Permanently')
            );
        } else {
            $new_actions['trash'] = sprintf(
                '<a href="%s" class="submitdelete" aria-label="%s">%s</a>',
                get_delete_post_link($post->ID),
                /* translators: %s: Post title. */
                esc_attr(sprintf(__('Move &#8220;%s&#8221; to the Trash'), $post->post_title)),
                __('Trash')
            );
        }

        return $new_actions;
    }

    /**
     * Kupon düzenleme yetkilerini güçlendir
     */
    public function fix_coupon_edit_capabilities($caps, $cap, $user_id, $args) {
        // Sadece admin panelinde çalışsın
        if (!is_admin()) {
            return $caps;
        }

        $user = get_userdata($user_id);
        if (!$user) {
            return $caps;
        }

        // Admin kullanıcıları hiçbir zaman etkilenmez
        if (in_array('administrator', $user->roles)) {
            return $caps;
        }

        // Sadece tutor instructor rolündeki kullanıcılar için
        if (!in_array(self::TUTOR_INSTRUCTOR_ROLE, $user->roles)) {
            return $caps;
        }

        // Kupon ile ilgili tüm yetkileri bypass et
        $coupon_caps = [
            'edit_post', 'delete_post', 'read_post', 'publish_post',
            'edit_shop_coupons', 'edit_others_shop_coupons', 'edit_private_shop_coupons', 'edit_published_shop_coupons',
            'delete_shop_coupons', 'delete_others_shop_coupons', 'delete_private_shop_coupons', 'delete_published_shop_coupons',
            'read_shop_coupons', 'read_private_shop_coupons',
            'publish_shop_coupons'
        ];

        if (in_array($cap, $coupon_caps)) {
            // Post ID'si varsa ve kuponsa
            if (isset($args[0])) {
                $post_id = $args[0];
                $post = get_post($post_id);

                if ($post && $post->post_type === 'shop_coupon') {
                    // Tutor Instructor kullanıcıları tüm kuponları düzenleyebilir
                    return array('exist');
                }
            } else {
                // Genel kupon yetkileri için
                return array('exist');
            }
        }

        return $caps;
    }

    /**
     * JavaScript ile ürün ve kupon row actions'larını düzelt
     */
    public function add_product_row_actions_js() {
        // Sadece tutor instructor rolündeki kullanıcılar için
        if (!$this->is_current_user_tutor_instructor()) {
            return;
        }

        // Sadece ürünler ve kuponlar sayfasında
        global $typenow;
        if (!in_array($typenow, ['product', 'shop_coupon'])) {
            return;
        }

        ?>
        <script type="text/javascript">
        jQuery(document).ready(function($) {
            // Ürün ve kupon satırlarını kontrol et ve eksik row actions'ları ekle
            $('.wp-list-table tbody tr').each(function() {
                var $row = $(this);
                var $rowActions = $row.find('.row-actions');
                var postId = $row.attr('id');

                if (postId && postId.indexOf('post-') === 0) {
                    var actualPostId = postId.replace('post-', '');

                    // Düzenle linki var mı kontrol et
                    if ($rowActions.find('.edit').length === 0) {
                        var editUrl = '<?php echo admin_url("post.php?action=edit&post="); ?>' + actualPostId;
                        var editLink = '<span class="edit"><a href="' + editUrl + '" aria-label="Düzenle">Düzenle</a> | </span>';
                        $rowActions.prepend(editLink);
                    }

                    // Hızlı düzenle linki var mı kontrol et
                    if ($rowActions.find('.inline').length === 0) {
                        var quickEditLink = '<span class="inline hide-if-no-js"><button type="button" class="button-link editinline" aria-label="Hızlı düzenle" aria-expanded="false">Hızlı Düzenle</button> | </span>';
                        $rowActions.find('.edit').after(quickEditLink);
                    }
                }
            });

            // Yeni ürün/kupon satırları eklendiğinde de çalıştır (AJAX sonrası)
            $(document).ajaxComplete(function() {
                setTimeout(function() {
                    $('.wp-list-table tbody tr').each(function() {
                        var $row = $(this);
                        var $rowActions = $row.find('.row-actions');
                        var postId = $row.attr('id');

                        if (postId && postId.indexOf('post-') === 0) {
                            var actualPostId = postId.replace('post-', '');

                            if ($rowActions.find('.edit').length === 0) {
                                var editUrl = '<?php echo admin_url("post.php?action=edit&post="); ?>' + actualPostId;
                                var editLink = '<span class="edit"><a href="' + editUrl + '" aria-label="Düzenle">Düzenle</a> | </span>';
                                $rowActions.prepend(editLink);
                            }

                            if ($rowActions.find('.inline').length === 0) {
                                var quickEditLink = '<span class="inline hide-if-no-js"><button type="button" class="button-link editinline" aria-label="Hızlı düzenle" aria-expanded="false">Hızlı Düzenle</button> | </span>';
                                $rowActions.find('.edit').after(quickEditLink);
                            }
                        }
                    });
                }, 100);
            });
        });
        </script>
        <?php
    }

    /**
     * Ürünler sayfasında view'ları filtrele
     */
    public function filter_product_views($views) {
        $current_user_id = get_current_user_id();

        // Kullanıcının toplam ürün sayısını al
        $user_products_count = count($this->get_user_product_ids($current_user_id));

        // Sadece kullanıcının ürün sayısını göster
        if (isset($views['all'])) {
            $views['all'] = str_replace(
                preg_replace('/\((\d+)\)/', '(' . $user_products_count . ')', $views['all']),
                $views['all'],
                $views['all']
            );
        }

        return $views;
    }

    /**
     * Kuponlar sayfasında view'ları filtrele
     */
    public function filter_coupon_views($views) {
        $current_user_id = get_current_user_id();

        // Kullanıcının toplam kupon sayısını al
        $user_coupons = get_posts([
            'post_type' => 'shop_coupon',
            'author' => $current_user_id,
            'posts_per_page' => -1,
            'fields' => 'ids',
            'post_status' => 'any'
        ]);

        $user_coupons_count = count($user_coupons);

        // Sadece kullanıcının kupon sayısını göster
        if (isset($views['all'])) {
            $views['all'] = str_replace(
                preg_replace('/\((\d+)\)/', '(' . $user_coupons_count . ')', $views['all']),
                $views['all'],
                $views['all']
            );
        }

        return $views;
    }

    /**
     * Siparişler sayfasında view'ları filtrele
     */
    public function filter_order_views($views) {
        // Bu fonksiyon siparişler için view sayılarını güncelleyebilir
        // Şimdilik basit bir implementasyon
        return $views;
    }

    /**
     * Postlar sayfasında view'ları filtrele
     */
    public function filter_post_views($views) {
        // Bu fonksiyon postlar için view sayılarını güncelleyebilir
        // Şimdilik basit bir implementasyon
        return $views;
    }

    /**
     * Tutor instructor kullanıcılarının kendi postlarını düzenlemesine izin ver
     */
    public function allow_instructor_edit_own_posts($caps, $cap, $user_id, $args) {
        // Sadece tutor instructor rolündeki kullanıcılar için
        if (!$this->is_user_tutor_instructor($user_id)) {
            return $caps;
        }

        // Role Custom ayarlarını al
        $role_custom_options = get_option('role_custom_settings', []);
        $is_enabled = isset($role_custom_options['instructors_can_publish_posts']) ? $role_custom_options['instructors_can_publish_posts'] : 1;

        // Post düzenleme yetkilerini kontrol et
        if (in_array($cap, ['edit_post', 'edit_published_post', 'delete_post', 'delete_published_post'])) {
            // Post ID'sini al
            if (isset($args[0])) {
                $post_id = $args[0];
                $post = get_post($post_id);

                // Eğer post varsa ve kullanıcı post sahibiyse
                if ($post && $post->post_type === 'post' && $post->post_author == $user_id) {
                    // Ayar etkinse veya post yayınlanmışsa düzenleme izni ver
                    if ($is_enabled || $post->post_status === 'publish') {
                        return ['exist']; // Yetki ver
                    }
                    // Ayar pasifse ve post pending durumundaysa düzenleme izni verme
                    if (!$is_enabled && $post->post_status === 'pending') {
                        return ['do_not_allow']; // Yetki verme
                    }
                }
            }
        }

        return $caps;
    }

    /**
     * Kullanıcının ürün ID'lerini al (yardımcı fonksiyon)
     */
    private function get_user_product_ids($user_id) {
        return get_posts([
            'post_type' => 'product',
            'author' => $user_id,
            'posts_per_page' => -1,
            'fields' => 'ids',
            'post_status' => 'any'
        ]);
    }

    /**
     * Siparişler sayfasında olup olmadığını kontrol et
     */
    private function is_orders_page() {
        global $pagenow;

        // Klasik siparişler sayfası
        if ($pagenow === 'edit.php' && isset($_GET['post_type']) && $_GET['post_type'] === 'shop_order') {
            return true;
        }

        // WooCommerce admin siparişler sayfası
        if ($pagenow === 'admin.php' && isset($_GET['page']) && $_GET['page'] === 'wc-orders') {
            return true;
        }

        // HPOS siparişler sayfası
        if (isset($_GET['page']) && $_GET['page'] === 'wc-orders') {
            return true;
        }

        return false;
    }

    /**
     * Siparişler sayfası için özel handler
     */
    public function handle_orders_screen($current_screen) {
        // Sadece tutor instructor rolündeki kullanıcılar için
        if (!$this->is_current_user_tutor_instructor()) {
            return;
        }

        // Siparişler sayfası kontrolü
        if (!isset($current_screen->id)) {
            return;
        }

        // WooCommerce siparişler sayfası
        if ($current_screen->id === 'woocommerce_page_wc-orders' ||
            $current_screen->id === 'edit-shop_order' ||
            strpos($current_screen->id, 'orders') !== false) {



            // Bu sayfada özel filtreleme uygula
            add_filter('woocommerce_order_list_table_prepare_items_query_args', [$this, 'filter_order_list_query_args'], 10, 1);
        }
    }

    /**
     * Sipariş listesi sorgu argümanlarını filtrele
     */
    public function filter_order_list_query_args($args) {
        // Sadece tutor instructor rolündeki kullanıcılar için
        if (!$this->is_current_user_tutor_instructor()) {
            return $args;
        }

        $current_user_id = get_current_user_id();


        // Kullanıcının ürünlerini al
        $user_products = $this->get_user_product_ids($current_user_id);

        if (empty($user_products)) {
            // Kullanıcının hiç ürünü yoksa hiçbir sipariş döndürme
            $args['include'] = [0];
            return $args;
        }

        // Kullanıcının ürünlerini içeren siparişleri bul
        $order_ids = $this->get_orders_with_user_products($user_products);

        if (!empty($order_ids)) {
            $args['include'] = $order_ids;
        } else {
            $args['include'] = [0];
        }

        return $args;
    }

    /**
     * HPOS sipariş listesi için filtreleme
     */
    public function filter_hpos_order_list() {
        // Sadece tutor instructor rolündeki kullanıcılar için
        if (!$this->is_current_user_tutor_instructor()) {
            return;
        }


    }

    /**
     * WooCommerce sipariş sorgu argümanlarını filtrele
     */
    public function filter_order_query_args($args) {
        // Sadece admin panelinde çalışsın
        if (!is_admin()) {
            return $args;
        }

        // Sadece tutor instructor rolündeki kullanıcılar için
        if (!$this->is_current_user_tutor_instructor()) {
            return $args;
        }

        // Mevcut kullanıcı ID'sini al
        $current_user_id = get_current_user_id();



        // Kullanıcının ürünlerini al
        $user_products = $this->get_user_product_ids($current_user_id);

        if (empty($user_products)) {
            // Kullanıcının hiç ürünü yoksa hiçbir sipariş gösterme
            $args['post__in'] = [0]; // Hiçbir sipariş döndürme
            return $args;
        }

        // Kullanıcının ürünlerini içeren siparişleri bul
        $order_ids = $this->get_orders_with_user_products($user_products);

        if (!empty($order_ids)) {
            $args['post__in'] = $order_ids;
        } else {
            $args['post__in'] = [0]; // Hiçbir sipariş döndürme
        }

        return $args;
    }

    /**
     * Kullanıcının ürünlerini içeren siparişleri bul
     */
    private function get_orders_with_user_products($product_ids) {
        global $wpdb;

        if (empty($product_ids)) {
            return [];
        }

        $product_ids_str = implode(',', array_map('intval', $product_ids));

        $order_ids = $wpdb->get_col("
            SELECT DISTINCT order_items.order_id
            FROM {$wpdb->prefix}woocommerce_order_items as order_items
            INNER JOIN {$wpdb->prefix}woocommerce_order_itemmeta as order_item_meta
                ON order_items.order_item_id = order_item_meta.order_item_id
            WHERE order_item_meta.meta_key = '_product_id'
                AND order_item_meta.meta_value IN ({$product_ids_str})
        ");

        return array_map('intval', $order_ids);
    }

    /**
     * wc_get_orders argümanlarını filtrele
     */
    public function filter_wc_get_orders_args($args) {
        // Sadece admin panelinde çalışsın
        if (!is_admin()) {
            return $args;
        }

        // Sadece tutor instructor rolündeki kullanıcılar için
        if (!$this->is_current_user_tutor_instructor()) {
            return $args;
        }

        $current_user_id = get_current_user_id();


        // Kullanıcının ürünlerini al
        $user_products = $this->get_user_product_ids($current_user_id);

        if (empty($user_products)) {
            // Kullanıcının hiç ürünü yoksa hiçbir sipariş döndürme
            $args['include'] = [0];
            return $args;
        }

        // Kullanıcının ürünlerini içeren siparişleri bul
        $order_ids = $this->get_orders_with_user_products($user_products);

        if (!empty($order_ids)) {
            $args['include'] = $order_ids;
        } else {
            $args['include'] = [0];
        }

        return $args;
    }

    /**
     * HPOS siparişlerini doğrudan filtrele
     */
    public function filter_hpos_orders_query($query, $query_vars) {
        // Sadece admin panelinde çalışsın
        if (!is_admin()) {
            return $query;
        }

        // Sadece tutor instructor rolündeki kullanıcılar için
        if (!$this->is_current_user_tutor_instructor()) {
            return $query;
        }

        $current_user_id = get_current_user_id();

        // Kullanıcının ürünlerini al
        $user_products = $this->get_user_product_ids($current_user_id);

        if (empty($user_products)) {
            // Kullanıcının hiç ürünü yoksa hiçbir sipariş döndürme
            $query['post__in'] = [0];
            return $query;
        }

        // Kullanıcının ürünlerini içeren siparişleri bul
        $order_ids = $this->get_orders_with_user_products($user_products);

        if (!empty($order_ids)) {
            $query['post__in'] = $order_ids;
        } else {
            $query['post__in'] = [0];
        }

        return $query;
    }

    /**
     * HPOS tablo sorgusunu filtrele
     */
    public function filter_hpos_table_query($query) {
        // Sadece admin panelinde çalışsın
        if (!is_admin()) {
            return;
        }

        // Sadece tutor instructor rolündeki kullanıcılar için
        if (!$this->is_current_user_tutor_instructor()) {
            return;
        }

        $current_user_id = get_current_user_id();

        // Kullanıcının ürünlerini al
        $user_products = $this->get_user_product_ids($current_user_id);

        if (empty($user_products)) {
            // Kullanıcının hiç ürünü yoksa hiçbir sipariş döndürme
            $query->set('post__in', [0]);
            return;
        }

        // Kullanıcının ürünlerini içeren siparişleri bul
        $order_ids = $this->get_orders_with_user_products($user_products);

        if (!empty($order_ids)) {
            $query->set('post__in', $order_ids);
        } else {
            $query->set('post__in', [0]);
        }
    }

    /**
     * JavaScript ile sipariş filtreleme ekle
     */
    public function add_order_filtering_js() {
        // Sadece tutor instructor rolündeki kullanıcılar için
        if (!$this->is_current_user_tutor_instructor()) {
            return;
        }

        // Sadece siparişler sayfasında
        if (!$this->is_orders_page()) {
            return;
        }

        $current_user_id = get_current_user_id();
        $user_products = $this->get_user_product_ids($current_user_id);

        if (empty($user_products)) {
            // Kullanıcının ürünü yoksa tüm siparişleri gizle
            ?>
            <script type="text/javascript">
            jQuery(document).ready(function($) {
                // Tüm sipariş satırlarını gizle
                $('.wp-list-table tbody tr').hide();

                // Bilgi mesajı ekle
                if ($('.wp-list-table tbody tr').length > 0) {
                    $('.wp-list-table tbody').prepend(
                        '<tr><td colspan="10" style="text-align: center; padding: 20px; background: #f9f9f9;">' +
                        '<strong>Bu kullanıcının henüz ürünü bulunmadığı için sipariş görüntülenemiyor.</strong>' +
                        '</td></tr>'
                    );
                }
            });
            </script>
            <?php
            return;
        }

        $order_ids = $this->get_orders_with_user_products($user_products);
        $order_ids_json = json_encode($order_ids);

        ?>
        <script type="text/javascript">
        jQuery(document).ready(function($) {
            var allowedOrderIds = <?php echo $order_ids_json; ?>;

            // Sipariş satırlarını filtrele
            $('.wp-list-table tbody tr').each(function() {
                var $row = $(this);
                var rowId = $row.attr('id');

                if (rowId && rowId.indexOf('post-') === 0) {
                    var orderId = parseInt(rowId.replace('post-', ''));

                    if (allowedOrderIds.indexOf(orderId) === -1) {
                        $row.hide();
                    }
                }
            });

            // AJAX sonrası da çalıştır
            $(document).ajaxComplete(function() {
                setTimeout(function() {
                    $('.wp-list-table tbody tr').each(function() {
                        var $row = $(this);
                        var rowId = $row.attr('id');

                        if (rowId && rowId.indexOf('post-') === 0) {
                            var orderId = parseInt(rowId.replace('post-', ''));

                            if (allowedOrderIds.indexOf(orderId) === -1) {
                                $row.hide();
                            }
                        }
                    });
                }, 100);
            });
        });
        </script>
        <?php
    }

    /**
     * Kullanıcı düzenleme sayfasında rol değişikliği uyarısı ekle
     */
    public function add_role_change_warning_js() {
        global $user_id;

        if (!$user_id) {
            return;
        }

        $user = get_userdata($user_id);
        if (!$user) {
            return;
        }

        $has_tutor_instructor = in_array(self::TUTOR_INSTRUCTOR_ROLE, $user->roles);

        ?>
        <script type="text/javascript">
        jQuery(document).ready(function($) {
            var hasTutorInstructor = <?php echo $has_tutor_instructor ? 'true' : 'false'; ?>;
            var originalRole = '<?php echo implode(',', $user->roles); ?>';

            // Rol değişikliği uyarısı
            $('#role, select[name="role"]').on('change', function() {
                var newRole = $(this).val();

                if (hasTutorInstructor && newRole !== 'tutor_instructor') {
                    if (confirm('Bu kullanıcı tutor instructor rolünden çıkarılıyor. Eğitmen yetkilerini kaybedecek. Devam etmek istiyor musunuz?')) {
                        // Devam et
                    } else {
                        // Geri al
                        $(this).val('tutor_instructor');
                        return false;
                    }
                }

                if (!hasTutorInstructor && newRole === 'tutor_instructor') {
                    alert('Bu kullanıcı tutor instructor rolüne geçiriliyor. Eğitmen yetkilerine sahip olacak.');
                }
            });

            // Form submit edildiğinde cache temizleme uyarısı
            $('#your-profile').on('submit', function() {
                var currentRole = $('#role, select[name="role"]').val();

                if ((hasTutorInstructor && currentRole !== 'tutor_instructor') || (!hasTutorInstructor && currentRole === 'tutor_instructor')) {
                    // Rol değişikliği var, kullanıcıyı bilgilendir
                    $(this).append('<input type="hidden" name="role_changed" value="1">');
                }
            });
        });
        </script>
        <?php
    }

    /**
     * Reports menü sayfasını ekle
     */
    public function add_reports_menu() {
        // Sadece Tutor Instructor rolündeki kullanıcılar için
        if (!$this->is_current_user_tutor_instructor()) {
            return;
        }

        // WooCommerce aktif değilse çık
        if (!class_exists('WooCommerce')) {
            return;
        }

        // Reports ana menüsü
        add_menu_page(
            __('Raporlar', 'role-custom'),             // Sayfa başlığı
            __('Raporlar', 'role-custom'),             // Menü başlığı
            'view_woocommerce_reports',                // Yetki
            'role-custom-reports',                     // Menü slug'ı
            [$this, 'render_reports_page'],            // Callback fonksiyon
            'dashicons-chart-area',                    // İkon
            60                                         // Pozisyon
        );
    }

    /**
     * Reports sayfasını render et
     */
    public function render_reports_page() {
        // Güvenlik kontrolü
        if (!current_user_can('view_woocommerce_reports') || !$this->is_current_user_tutor_instructor()) {
            wp_die(__('Bu sayfaya erişim yetkiniz yok.', 'role-custom'));
        }

        // WooCommerce aktif değilse uyarı göster
        if (!class_exists('WooCommerce')) {
            echo '<div class="notice notice-error"><p>' . __('WooCommerce eklentisi aktif değil.', 'role-custom') . '</p></div>';
            return;
        }

        // Kullanıcının ürün sayısını kontrol et
        $user_products = $this->get_user_products();
        if (empty($user_products)) {
            echo '<div class="wrap">';
            echo '<h1>' . __('Raporlar', 'role-custom') . '</h1>';
            echo '<div class="notice notice-info"><p>' . __('Henüz hiç ürününüz yok. Raporları görmek için önce ürün oluşturun.', 'role-custom') . '</p></div>';
            echo '</div>';
            return;
        }

        // Reports sayfası HTML'ini render et
        $this->render_reports_html();
    }

    /**
     * Kullanıcının ürünlerini al
     */
    private function get_user_products() {
        $current_user_id = get_current_user_id();

        // Güvenlik kontrolü - sadece tutor instructor kullanıcıları
        if (!$this->is_current_user_tutor_instructor()) {
            return [];
        }

        $args = [
            'post_type' => 'product',
            'post_status' => 'any',
            'author' => $current_user_id,
            'posts_per_page' => -1,
            'fields' => 'ids'
        ];

        $products = get_posts($args);

        // Ek güvenlik: Ürünlerin gerçekten bu kullanıcıya ait olduğunu doğrula
        $verified_products = [];
        foreach ($products as $product_id) {
            $product_author = get_post_field('post_author', $product_id);
            if ($product_author == $current_user_id) {
                $verified_products[] = $product_id;
            }
        }

        return $verified_products;
    }

    /**
     * Reports sayfası HTML'ini render et
     */
    private function render_reports_html() {
        ?>
        <div class="wrap role-custom-reports">
            <h1><?php _e('Raporlar', 'role-custom'); ?></h1>

            <!-- Özet Kartları -->
            <div class="role-custom-summary-cards">
                <div class="summary-card">
                    <div class="card-icon">
                        <span class="dashicons dashicons-cart"></span>
                    </div>
                    <div class="card-content">
                        <h3 id="total-orders">-</h3>
                        <p><?php _e('Toplam Sipariş', 'role-custom'); ?></p>
                    </div>
                </div>

                <div class="summary-card">
                    <div class="card-icon">
                        <span class="dashicons dashicons-money-alt"></span>
                    </div>
                    <div class="card-content">
                        <h3 id="total-revenue">-</h3>
                        <p><?php _e('Toplam Gelir', 'role-custom'); ?></p>
                    </div>
                </div>

                <div class="summary-card">
                    <div class="card-icon">
                        <span class="dashicons dashicons-products"></span>
                    </div>
                    <div class="card-content">
                        <h3 id="total-products">-</h3>
                        <p><?php _e('Toplam Ürün', 'role-custom'); ?></p>
                    </div>
                </div>

                <div class="summary-card">
                    <div class="card-icon">
                        <span class="dashicons dashicons-star-filled"></span>
                    </div>
                    <div class="card-content">
                        <h3 id="avg-rating">-</h3>
                        <p><?php _e('Ortalama Puan', 'role-custom'); ?></p>
                    </div>
                </div>
            </div>

            <!-- Kurs İstatistikleri -->
            <h2><?php _e('Kurs İstatistikleri', 'role-custom'); ?></h2>
            <div class="role-custom-summary-cards">
                <div class="summary-card">
                    <div class="card-icon">
                        <span class="dashicons dashicons-book"></span>
                    </div>
                    <div class="card-content">
                        <h3 id="total-courses">-</h3>
                        <p><?php _e('Toplam Kurs', 'role-custom'); ?></p>
                    </div>
                </div>

                <div class="summary-card">
                    <div class="card-icon">
                        <span class="dashicons dashicons-visibility"></span>
                    </div>
                    <div class="card-content">
                        <h3 id="published-courses">-</h3>
                        <p><?php _e('Yayınlanan Kurs', 'role-custom'); ?></p>
                    </div>
                </div>

                <div class="summary-card">
                    <div class="card-icon">
                        <span class="dashicons dashicons-groups"></span>
                    </div>
                    <div class="card-content">
                        <h3 id="total-students">-</h3>
                        <p><?php _e('Toplam Öğrenci', 'role-custom'); ?></p>
                    </div>
                </div>

                <div class="summary-card">
                    <div class="card-icon">
                        <span class="dashicons dashicons-media-document"></span>
                    </div>
                    <div class="card-content">
                        <h3 id="total-lessons">-</h3>
                        <p><?php _e('Toplam Ders', 'role-custom'); ?></p>
                    </div>
                </div>

                <div class="summary-card">
                    <div class="card-icon">
                        <span class="dashicons dashicons-clipboard"></span>
                    </div>
                    <div class="card-content">
                        <h3 id="total-quizzes">-</h3>
                        <p><?php _e('Toplam Quiz', 'role-custom'); ?></p>
                    </div>
                </div>

                <div class="summary-card">
                    <div class="card-icon">
                        <span class="dashicons dashicons-star-half"></span>
                    </div>
                    <div class="card-content">
                        <h3 id="avg-course-rating">-</h3>
                        <p><?php _e('Kurs Puanı', 'role-custom'); ?></p>
                    </div>
                </div>
            </div>

            <!-- Grafik Alanları -->
            <div class="role-custom-charts-container">
                <!-- Satış Grafiği -->
                <div class="chart-section">
                    <div class="chart-header">
                        <h2><?php _e('Satış Trendi', 'role-custom'); ?></h2>
                        <div class="chart-controls">
                            <select id="sales-period">
                                <option value="7"><?php _e('Son 7 Gün', 'role-custom'); ?></option>
                                <option value="30" selected><?php _e('Son 30 Gün', 'role-custom'); ?></option>
                                <option value="90"><?php _e('Son 90 Gün', 'role-custom'); ?></option>
                                <option value="custom"><?php _e('Özel Tarih Aralığı', 'role-custom'); ?></option>
                            </select>
                            <div id="sales-custom-dates" class="custom-date-range" style="display: none;">
                                <input type="date" id="sales-start-date" />
                                <span><?php _e('ile', 'role-custom'); ?></span>
                                <input type="date" id="sales-end-date" />
                                <button type="button" id="sales-apply-dates" class="button button-primary"><?php _e('Uygula', 'role-custom'); ?></button>
                            </div>
                        </div>
                    </div>
                    <div class="chart-container">
                        <canvas id="sales-chart"></canvas>
                    </div>
                </div>

                <!-- Gelir Grafiği -->
                <div class="chart-section">
                    <div class="chart-header">
                        <h2><?php _e('Gelir Trendi', 'role-custom'); ?></h2>
                        <div class="chart-controls">
                            <select id="revenue-period">
                                <option value="7"><?php _e('Son 7 Gün', 'role-custom'); ?></option>
                                <option value="30" selected><?php _e('Son 30 Gün', 'role-custom'); ?></option>
                                <option value="90"><?php _e('Son 90 Gün', 'role-custom'); ?></option>
                                <option value="custom"><?php _e('Özel Tarih Aralığı', 'role-custom'); ?></option>
                            </select>
                            <div id="revenue-custom-dates" class="custom-date-range" style="display: none;">
                                <input type="date" id="revenue-start-date" />
                                <span><?php _e('ile', 'role-custom'); ?></span>
                                <input type="date" id="revenue-end-date" />
                                <button type="button" id="revenue-apply-dates" class="button button-primary"><?php _e('Uygula', 'role-custom'); ?></button>
                            </div>
                        </div>
                    </div>
                    <div class="chart-container">
                        <canvas id="revenue-chart"></canvas>
                    </div>
                </div>

                <!-- En Çok Satan Ürünler -->
                <div class="chart-section">
                    <div class="chart-header">
                        <h2><?php _e('En Çok Satan Ürünler', 'role-custom'); ?></h2>
                    </div>
                    <div class="chart-container">
                        <canvas id="products-chart"></canvas>
                    </div>
                </div>

                <!-- Kurs Performansı -->
                <div class="chart-section">
                    <div class="chart-header">
                        <h2><?php _e('Kurs Performansı', 'role-custom'); ?></h2>
                    </div>
                    <div class="chart-container">
                        <canvas id="courses-chart"></canvas>
                    </div>
                </div>
            </div>


        </div>
        <?php
    }

    /**
     * Kullanıcının sipariş verilerini al
     */
    private function get_user_orders_data($days = 30) {
        $current_user_id = get_current_user_id();
        $user_products = $this->get_user_products();

        if (empty($user_products)) {
            return [];
        }

        // Tarih aralığını hesapla
        $end_date = current_time('Y-m-d');
        $start_date = date('Y-m-d', strtotime("-{$days} days"));

        global $wpdb;

        // WooCommerce Analytics tablosunu kullan (daha doğru gelir hesaplaması)
        $product_lookup_table = $wpdb->prefix . 'wc_order_product_lookup';

        // Analytics tablosu var mı kontrol et
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$product_lookup_table}'");

        if ($table_exists) {
            // WooCommerce Analytics tablosunu kullan
            $query = $wpdb->prepare("
                SELECT
                    DATE(pl.date_created) as order_date,
                    COUNT(DISTINCT pl.order_id) as order_count,
                    SUM(pl.product_net_revenue) as total_revenue,
                    SUM(pl.product_qty) as total_quantity
                FROM {$product_lookup_table} pl
                INNER JOIN {$wpdb->prefix}wc_order_stats os ON pl.order_id = os.order_id
                WHERE os.status IN ('wc-completed', 'wc-processing', 'wc-on-hold')
                    AND DATE(pl.date_created) BETWEEN %s AND %s
                    AND pl.product_id IN (" . implode(',', array_map('intval', $user_products)) . ")
                GROUP BY DATE(pl.date_created)
                ORDER BY order_date ASC
            ", $start_date, $end_date);
        } else {
            // Fallback: Geleneksel yöntem
            $query = $wpdb->prepare("
                SELECT
                    DATE(p.post_date) as order_date,
                    COUNT(DISTINCT p.ID) as order_count,
                    SUM(CAST(oim_total.meta_value AS DECIMAL(10,2))) as total_revenue,
                    SUM(CAST(oim_qty.meta_value AS SIGNED)) as total_quantity
                FROM {$wpdb->posts} p
                INNER JOIN {$wpdb->prefix}woocommerce_order_items oi ON p.ID = oi.order_id
                INNER JOIN {$wpdb->prefix}woocommerce_order_itemmeta oim_product ON oi.order_item_id = oim_product.order_item_id
                    AND oim_product.meta_key = '_product_id'
                LEFT JOIN {$wpdb->prefix}woocommerce_order_itemmeta oim_total ON oi.order_item_id = oim_total.order_item_id
                    AND oim_total.meta_key = '_line_total'
                LEFT JOIN {$wpdb->prefix}woocommerce_order_itemmeta oim_qty ON oi.order_item_id = oim_qty.order_item_id
                    AND oim_qty.meta_key = '_qty'
                WHERE p.post_type = 'shop_order'
                    AND p.post_status IN ('wc-completed', 'wc-processing', 'wc-on-hold')
                    AND DATE(p.post_date) BETWEEN %s AND %s
                    AND CAST(oim_product.meta_value AS UNSIGNED) IN (" . implode(',', array_map('intval', $user_products)) . ")
                GROUP BY DATE(p.post_date)
                ORDER BY order_date ASC
            ", $start_date, $end_date);
        }

        $results = $wpdb->get_results($query);

        // Eksik günleri sıfır değerlerle doldur
        $data = [];
        $current_date = $start_date;

        while ($current_date <= $end_date) {
            $found = false;
            foreach ($results as $result) {
                if ($result->order_date === $current_date) {
                    $data[] = [
                        'date' => $current_date,
                        'orders' => (int) $result->order_count,
                        'revenue' => (float) $result->total_revenue,
                        'quantity' => (int) $result->total_quantity
                    ];
                    $found = true;
                    break;
                }
            }

            if (!$found) {
                $data[] = [
                    'date' => $current_date,
                    'orders' => 0,
                    'revenue' => 0,
                    'quantity' => 0
                ];
            }

            $current_date = date('Y-m-d', strtotime($current_date . ' +1 day'));
        }

        return $data;
    }

    /**
     * Kullanıcının sipariş verilerini tarih aralığına göre al
     */
    private function get_user_orders_data_by_range($start_date, $end_date) {
        $current_user_id = get_current_user_id();
        $user_products = $this->get_user_products();

        if (empty($user_products)) {
            return [];
        }

        // Tarih formatını doğrula
        $start_date = date('Y-m-d', strtotime($start_date));
        $end_date = date('Y-m-d', strtotime($end_date));

        global $wpdb;

        // WooCommerce Analytics tablosunu kullan (daha doğru gelir hesaplaması)
        $product_lookup_table = $wpdb->prefix . 'wc_order_product_lookup';

        // Analytics tablosu var mı kontrol et
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$product_lookup_table}'");

        if ($table_exists) {
            // WooCommerce Analytics tablosunu kullan
            $query = $wpdb->prepare("
                SELECT
                    DATE(pl.date_created) as order_date,
                    COUNT(DISTINCT pl.order_id) as order_count,
                    SUM(pl.product_net_revenue) as total_revenue,
                    SUM(pl.product_qty) as total_quantity
                FROM {$product_lookup_table} pl
                INNER JOIN {$wpdb->prefix}wc_order_stats os ON pl.order_id = os.order_id
                WHERE os.status IN ('wc-completed', 'wc-processing', 'wc-on-hold')
                    AND DATE(pl.date_created) BETWEEN %s AND %s
                    AND pl.product_id IN (" . implode(',', array_map('intval', $user_products)) . ")
                GROUP BY DATE(pl.date_created)
                ORDER BY order_date ASC
            ", $start_date, $end_date);
        } else {
            // Fallback: Geleneksel yöntem
            $query = $wpdb->prepare("
                SELECT
                    DATE(p.post_date) as order_date,
                    COUNT(DISTINCT p.ID) as order_count,
                    SUM(CAST(oim_total.meta_value AS DECIMAL(10,2))) as total_revenue,
                    SUM(CAST(oim_qty.meta_value AS SIGNED)) as total_quantity
                FROM {$wpdb->posts} p
                INNER JOIN {$wpdb->prefix}woocommerce_order_items oi ON p.ID = oi.order_id
                INNER JOIN {$wpdb->prefix}woocommerce_order_itemmeta oim_product ON oi.order_item_id = oim_product.order_item_id
                    AND oim_product.meta_key = '_product_id'
                LEFT JOIN {$wpdb->prefix}woocommerce_order_itemmeta oim_total ON oi.order_item_id = oim_total.order_item_id
                    AND oim_total.meta_key = '_line_total'
                LEFT JOIN {$wpdb->prefix}woocommerce_order_itemmeta oim_qty ON oi.order_item_id = oim_qty.order_item_id
                    AND oim_qty.meta_key = '_qty'
                WHERE p.post_type = 'shop_order'
                    AND p.post_status IN ('wc-completed', 'wc-processing', 'wc-on-hold')
                    AND DATE(p.post_date) BETWEEN %s AND %s
                    AND CAST(oim_product.meta_value AS UNSIGNED) IN (" . implode(',', array_map('intval', $user_products)) . ")
                GROUP BY DATE(p.post_date)
                ORDER BY order_date ASC
            ", $start_date, $end_date);
        }

        $results = $wpdb->get_results($query);

        // Eksik günleri sıfır değerlerle doldur
        $data = [];
        $current_date = $start_date;

        while ($current_date <= $end_date) {
            $found = false;
            foreach ($results as $result) {
                if ($result->order_date === $current_date) {
                    $data[] = [
                        'date' => $current_date,
                        'orders' => (int) $result->order_count,
                        'revenue' => (float) $result->total_revenue,
                        'quantity' => (int) $result->total_quantity
                    ];
                    $found = true;
                    break;
                }
            }

            if (!$found) {
                $data[] = [
                    'date' => $current_date,
                    'orders' => 0,
                    'revenue' => 0,
                    'quantity' => 0
                ];
            }

            $current_date = date('Y-m-d', strtotime($current_date . ' +1 day'));
        }

        return $data;
    }

    /**
     * Kullanıcının en çok satan ürünlerini al
     */
    private function get_top_selling_products($limit = 10) {
        $current_user_id = get_current_user_id();
        $user_products = $this->get_user_products();

        if (empty($user_products)) {
            return [];
        }

        global $wpdb;

        // WooCommerce Analytics tablosunu kullan (daha doğru gelir hesaplaması)
        $product_lookup_table = $wpdb->prefix . 'wc_order_product_lookup';

        // Analytics tablosu var mı kontrol et
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$product_lookup_table}'");

        if ($table_exists) {
            // WooCommerce Analytics tablosunu kullan
            $query = $wpdb->prepare("
                SELECT
                    pl.product_id,
                    p.post_title as product_name,
                    SUM(pl.product_qty) as total_sold,
                    SUM(pl.product_net_revenue) as total_revenue
                FROM {$product_lookup_table} pl
                INNER JOIN {$wpdb->prefix}wc_order_stats os ON pl.order_id = os.order_id
                INNER JOIN {$wpdb->posts} p ON pl.product_id = p.ID
                WHERE os.status IN ('wc-completed', 'wc-processing', 'wc-on-hold')
                    AND pl.product_id IN (" . implode(',', array_map('intval', $user_products)) . ")
                GROUP BY pl.product_id, p.post_title
                ORDER BY total_sold DESC
                LIMIT %d
            ", $limit);
        } else {
            // Fallback: Geleneksel yöntem
            $query = $wpdb->prepare("
                SELECT
                    CAST(oim_product.meta_value AS UNSIGNED) as product_id,
                    p.post_title as product_name,
                    SUM(CAST(oim_qty.meta_value AS SIGNED)) as total_sold,
                    SUM(CAST(oim_total.meta_value AS DECIMAL(10,2))) as total_revenue
                FROM {$wpdb->posts} po
                INNER JOIN {$wpdb->prefix}woocommerce_order_items oi ON po.ID = oi.order_id
                INNER JOIN {$wpdb->prefix}woocommerce_order_itemmeta oim_product ON oi.order_item_id = oim_product.order_item_id
                    AND oim_product.meta_key = '_product_id'
                INNER JOIN {$wpdb->posts} p ON CAST(oim_product.meta_value AS UNSIGNED) = p.ID
                LEFT JOIN {$wpdb->prefix}woocommerce_order_itemmeta oim_total ON oi.order_item_id = oim_total.order_item_id
                    AND oim_total.meta_key = '_line_total'
                LEFT JOIN {$wpdb->prefix}woocommerce_order_itemmeta oim_qty ON oi.order_item_id = oim_qty.order_item_id
                    AND oim_qty.meta_key = '_qty'
                WHERE po.post_type = 'shop_order'
                    AND po.post_status IN ('wc-completed', 'wc-processing', 'wc-on-hold')
                    AND CAST(oim_product.meta_value AS UNSIGNED) IN (" . implode(',', array_map('intval', $user_products)) . ")
                GROUP BY product_id, product_name
                ORDER BY total_sold DESC
                LIMIT %d
            ", $limit);
        }

        return $wpdb->get_results($query);
    }

    /**
     * Kullanıcının genel istatistiklerini al
     */
    private function get_user_stats() {
        $current_user_id = get_current_user_id();
        $user_products = $this->get_user_products();

        $stats = [
            'total_products' => count($user_products),
            'total_orders' => 0,
            'total_revenue' => 0,
            'avg_rating' => 0
        ];

        if (empty($user_products)) {
            return $stats;
        }

        global $wpdb;

        // Toplam sipariş ve gelir hesapla
        // WooCommerce Analytics tablosunu kullan (daha doğru gelir hesaplaması)
        $product_lookup_table = $wpdb->prefix . 'wc_order_product_lookup';

        // Analytics tablosu var mı kontrol et
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$product_lookup_table}'");

        if ($table_exists) {
            // WooCommerce Analytics tablosunu kullan
            $query = "
                SELECT
                    COUNT(DISTINCT pl.order_id) as total_orders,
                    SUM(pl.product_net_revenue) as total_revenue
                FROM {$product_lookup_table} pl
                INNER JOIN {$wpdb->prefix}wc_order_stats os ON pl.order_id = os.order_id
                WHERE os.status IN ('wc-completed', 'wc-processing', 'wc-on-hold')
                    AND pl.product_id IN (" . implode(',', array_map('intval', $user_products)) . ")
            ";
        } else {
            // Fallback: Geleneksel yöntem
            $query = "
                SELECT
                    COUNT(DISTINCT p.ID) as total_orders,
                    SUM(CAST(oim_total.meta_value AS DECIMAL(10,2))) as total_revenue
                FROM {$wpdb->posts} p
                INNER JOIN {$wpdb->prefix}woocommerce_order_items oi ON p.ID = oi.order_id
                INNER JOIN {$wpdb->prefix}woocommerce_order_itemmeta oim_product ON oi.order_item_id = oim_product.order_item_id
                    AND oim_product.meta_key = '_product_id'
                LEFT JOIN {$wpdb->prefix}woocommerce_order_itemmeta oim_total ON oi.order_item_id = oim_total.order_item_id
                    AND oim_total.meta_key = '_line_total'
                WHERE p.post_type = 'shop_order'
                    AND p.post_status IN ('wc-completed', 'wc-processing', 'wc-on-hold')
                    AND CAST(oim_product.meta_value AS UNSIGNED) IN (" . implode(',', array_map('intval', $user_products)) . ")
            ";
        }

        $result = $wpdb->get_row($query);

        if ($result) {
            $stats['total_orders'] = (int) $result->total_orders;
            $stats['total_revenue'] = (float) $result->total_revenue;
        }

        // Ortalama puan hesapla
        $rating_query = "
            SELECT AVG(CAST(cm.meta_value AS DECIMAL(3,2))) as avg_rating
            FROM {$wpdb->comments} c
            INNER JOIN {$wpdb->commentmeta} cm ON c.comment_ID = cm.comment_id
            WHERE c.comment_type = 'review'
                AND c.comment_approved = '1'
                AND cm.meta_key = 'rating'
                AND c.comment_post_ID IN (" . implode(',', array_map('intval', $user_products)) . ")
        ";

        $rating_result = $wpdb->get_var($rating_query);
        $stats['avg_rating'] = $rating_result ? round((float) $rating_result, 1) : 0;

        return $stats;
    }

    /**
     * Kullanıcının kurs istatistiklerini al
     */
    private function get_user_course_stats() {
        $current_user_id = get_current_user_id();

        $stats = [
            'total_courses' => 0,
            'published_courses' => 0,
            'draft_courses' => 0,
            'total_students' => 0,
            'total_lessons' => 0,
            'total_quizzes' => 0,
            'avg_course_rating' => 0,
            'total_reviews' => 0
        ];

        // Kullanıcının kurslarını al
        $courses = get_posts([
            'post_type' => 'courses',
            'post_status' => 'any',
            'author' => $current_user_id,
            'posts_per_page' => -1,
            'fields' => 'ids'
        ]);

        if (empty($courses)) {
            return $stats;
        }

        $stats['total_courses'] = count($courses);

        // Kurs durumlarını say
        foreach ($courses as $course_id) {
            $course_status = get_post_status($course_id);
            if ($course_status === 'publish') {
                $stats['published_courses']++;
            } elseif ($course_status === 'draft') {
                $stats['draft_courses']++;
            }
        }

        global $wpdb;

        // Toplam öğrenci sayısı (Tutor LMS enrollment tablosu)
        if (function_exists('tutor')) {
            $enrollment_table = $wpdb->prefix . 'tutor_enrollments';
            $table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$enrollment_table}'");

            if ($table_exists) {
                $student_count = $wpdb->get_var("
                    SELECT COUNT(DISTINCT user_id)
                    FROM {$enrollment_table}
                    WHERE course_id IN (" . implode(',', array_map('intval', $courses)) . ")
                    AND post_status = 'completed'
                ");
                $stats['total_students'] = (int) $student_count;
            }
        }

        // Toplam ders sayısı
        $lessons = get_posts([
            'post_type' => 'lesson',
            'post_status' => 'publish',
            'meta_query' => [
                [
                    'key' => '_tutor_course_id_for_lesson',
                    'value' => $courses,
                    'compare' => 'IN'
                ]
            ],
            'posts_per_page' => -1,
            'fields' => 'ids'
        ]);
        $stats['total_lessons'] = count($lessons);

        // Toplam quiz sayısı
        $quizzes = get_posts([
            'post_type' => 'tutor_quiz',
            'post_status' => 'publish',
            'meta_query' => [
                [
                    'key' => '_tutor_course_id_for_quiz',
                    'value' => $courses,
                    'compare' => 'IN'
                ]
            ],
            'posts_per_page' => -1,
            'fields' => 'ids'
        ]);
        $stats['total_quizzes'] = count($quizzes);

        // Ortalama puan ve toplam yorum sayısı
        $rating_query = "
            SELECT
                AVG(CAST(cm.meta_value AS DECIMAL(3,2))) as avg_rating,
                COUNT(*) as total_reviews
            FROM {$wpdb->comments} c
            INNER JOIN {$wpdb->commentmeta} cm ON c.comment_ID = cm.comment_id
            WHERE c.comment_type = 'tutor_course_rating'
                AND c.comment_approved = '1'
                AND cm.meta_key = 'tutor_rating'
                AND c.comment_post_ID IN (" . implode(',', array_map('intval', $courses)) . ")
        ";

        $rating_result = $wpdb->get_row($rating_query);
        if ($rating_result) {
            $stats['avg_course_rating'] = $rating_result->avg_rating ? round((float) $rating_result->avg_rating, 1) : 0;
            $stats['total_reviews'] = (int) $rating_result->total_reviews;
        }

        return $stats;
    }

    /**
     * AJAX: Satış verilerini al
     */
    public function ajax_get_sales_data() {
        // Güvenlik kontrolü
        if (!current_user_can('view_woocommerce_reports') || !$this->is_current_user_tutor_instructor()) {
            wp_send_json_error(['message' => 'Unauthorized access']);
            return;
        }

        // Nonce kontrolü
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'role_custom_reports_nonce')) {
            wp_send_json_error(['message' => 'Invalid security token']);
            return;
        }

        // WooCommerce aktif kontrolü
        if (!class_exists('WooCommerce')) {
            wp_send_json_error(['message' => 'WooCommerce not active']);
            return;
        }

        // Tarih parametrelerini al
        $start_date = isset($_POST['start_date']) ? sanitize_text_field($_POST['start_date']) : '';
        $end_date = isset($_POST['end_date']) ? sanitize_text_field($_POST['end_date']) : '';
        $days = isset($_POST['days']) ? intval($_POST['days']) : 30;

        // Özel tarih aralığı kontrolü
        if (!empty($start_date) && !empty($end_date)) {
            $data = $this->get_user_orders_data_by_range($start_date, $end_date);
        } else {
            // Gün sayısını doğrula (1-365 arası)
            $days = max(1, min(365, $days));
            $data = $this->get_user_orders_data($days);
        }

        wp_send_json_success($data);
    }

    /**
     * AJAX: Gelir verilerini al
     */
    public function ajax_get_revenue_data() {
        // Güvenlik kontrolü
        if (!current_user_can('view_woocommerce_reports') || !$this->is_current_user_tutor_instructor()) {
            wp_send_json_error(['message' => 'Unauthorized access']);
            return;
        }

        // Nonce kontrolü
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'role_custom_reports_nonce')) {
            wp_send_json_error(['message' => 'Invalid security token']);
            return;
        }

        // WooCommerce aktif kontrolü
        if (!class_exists('WooCommerce')) {
            wp_send_json_error(['message' => 'WooCommerce not active']);
            return;
        }

        // Tarih parametrelerini al
        $start_date = isset($_POST['start_date']) ? sanitize_text_field($_POST['start_date']) : '';
        $end_date = isset($_POST['end_date']) ? sanitize_text_field($_POST['end_date']) : '';
        $days = isset($_POST['days']) ? intval($_POST['days']) : 30;

        // Özel tarih aralığı kontrolü
        if (!empty($start_date) && !empty($end_date)) {
            $data = $this->get_user_orders_data_by_range($start_date, $end_date);
        } else {
            // Gün sayısını doğrula (1-365 arası)
            $days = max(1, min(365, $days));
            $data = $this->get_user_orders_data($days);
        }

        wp_send_json_success($data);
    }

    /**
     * AJAX: Ürün performans verilerini al
     */
    public function ajax_get_product_performance() {
        // Güvenlik kontrolü
        if (!current_user_can('view_woocommerce_reports') || !$this->is_current_user_tutor_instructor()) {
            wp_send_json_error(['message' => 'Unauthorized access']);
            return;
        }

        // Nonce kontrolü
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'role_custom_reports_nonce')) {
            wp_send_json_error(['message' => 'Invalid security token']);
            return;
        }

        // WooCommerce aktif kontrolü
        if (!class_exists('WooCommerce')) {
            wp_send_json_error(['message' => 'WooCommerce not active']);
            return;
        }

        // Limit değerini doğrula (1-50 arası)
        $limit = isset($_POST['limit']) ? intval($_POST['limit']) : 10;
        $limit = max(1, min(50, $limit));

        $data = $this->get_top_selling_products($limit);

        wp_send_json_success($data);
    }

    /**
     * AJAX: Kurs verilerini al
     */
    public function ajax_get_courses_data() {
        // Güvenlik kontrolü
        if (!current_user_can('view_woocommerce_reports') || !$this->is_current_user_tutor_instructor()) {
            wp_send_json_error(['message' => 'Unauthorized access']);
            return;
        }

        if (!wp_verify_nonce($_POST['nonce'], 'role_custom_reports_nonce')) {
            wp_send_json_error(['message' => 'Invalid nonce']);
            return;
        }

        $courses = $this->get_top_courses();
        wp_send_json_success($courses);
    }

    /**
     * En popüler kursları al
     */
    private function get_top_courses($limit = 10) {
        $current_user_id = get_current_user_id();

        // Kullanıcının kurslarını al
        $courses = get_posts([
            'post_type' => 'courses',
            'post_status' => 'publish',
            'author' => $current_user_id,
            'posts_per_page' => $limit,
            'orderby' => 'date',
            'order' => 'DESC'
        ]);

        $course_data = [];

        foreach ($courses as $course) {
            // Öğrenci sayısını hesapla
            $student_count = 0;
            if (function_exists('tutor_utils')) {
                $student_count = tutor_utils()->get_course_enrolled_users_count($course->ID);
            }

            // Ortalama puanı al
            $rating = 0;
            global $wpdb;
            $rating_result = $wpdb->get_var($wpdb->prepare("
                SELECT AVG(CAST(cm.meta_value AS DECIMAL(3,2)))
                FROM {$wpdb->comments} c
                INNER JOIN {$wpdb->commentmeta} cm ON c.comment_ID = cm.comment_id
                WHERE c.comment_type = 'tutor_course_rating'
                    AND c.comment_approved = '1'
                    AND cm.meta_key = 'tutor_rating'
                    AND c.comment_post_ID = %d
            ", $course->ID));

            if ($rating_result) {
                $rating = round((float) $rating_result, 1);
            }

            $course_data[] = [
                'course_id' => $course->ID,
                'course_name' => $course->post_title,
                'student_count' => (int) $student_count,
                'rating' => $rating,
                'status' => $course->post_status
            ];
        }

        return $course_data;
    }

    /**
     * Reports sayfası için script ve style dosyalarını yükle
     */
    public function enqueue_reports_scripts($hook) {
        // Sadece reports sayfasında yükle
        if ($hook !== 'toplevel_page_role-custom-reports') {
            return;
        }

        // Sadece Tutor Instructor rolündeki kullanıcılar için
        if (!$this->is_current_user_tutor_instructor()) {
            return;
        }

        // Chart.js kütüphanesini yükle
        wp_enqueue_script(
            'chartjs',
            'https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js',
            [],
            '3.9.1',
            true
        );

        // Reports JavaScript dosyasını yükle
        wp_enqueue_script(
            'role-custom-reports',
            ROLE_CUSTOM_PLUGIN_URL . 'assets/js/reports.js',
            ['jquery', 'chartjs'],
            ROLE_CUSTOM_VERSION,
            true
        );

        // Reports CSS dosyasını yükle
        wp_enqueue_style(
            'role-custom-reports',
            ROLE_CUSTOM_PLUGIN_URL . 'assets/css/reports.css',
            [],
            ROLE_CUSTOM_VERSION
        );

        // JavaScript'e veri gönder
        wp_localize_script('role-custom-reports', 'roleCustomReports', [
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('role_custom_reports_nonce'),
            'strings' => [
                'error' => __('Veri yüklenirken hata oluştu.', 'role-custom'),
                'noData' => __('Gösterilecek veri yok.', 'role-custom'),
                'orders' => __('Siparişler', 'role-custom'),
                'revenue' => __('Gelir', 'role-custom'),
                'quantity' => __('Adet', 'role-custom'),
                'currency' => 'TL'
            ]
        ]);

        // Sayfa yüklendiğinde genel istatistikleri al
        $stats = $this->get_user_stats();
        wp_localize_script('role-custom-reports', 'roleCustomStats', $stats);

        // Kurs istatistiklerini al
        $course_stats = $this->get_user_course_stats();
        wp_localize_script('role-custom-reports', 'roleCustomCourseStats', $course_stats);
    }

    /**
     * WooCommerce değerlendirmeler sayfası için ekran kontrolü
     */
    public function handle_reviews_screen() {
        $screen = get_current_screen();

        // Değerlendirmeler sayfası kontrolü
        if ($screen && $screen->id === 'product_page_product-reviews') {
            // Sadece tutor instructor rolündeki kullanıcılar için
            if ($this->is_current_user_tutor_instructor()) {
                // Bu sayfada filtreleme aktif olacak
                add_filter('comments_clauses', [$this, 'filter_product_reviews_by_user_products'], 10, 2);
            }
        }
    }

    /**
     * Ürün değerlendirmelerini kullanıcının ürünlerine göre filtrele
     */
    public function filter_product_reviews_by_user_products($clauses, $comment_query) {
        global $wpdb;

        // Sadece admin panelinde çalışsın
        if (!is_admin()) {
            return $clauses;
        }

        // Sadece tutor instructor rolündeki kullanıcılar için
        if (!$this->is_current_user_tutor_instructor()) {
            return $clauses;
        }

        // Sadece değerlendirmeler sayfasında çalışsın
        $screen = get_current_screen();
        if (!$screen || $screen->id !== 'product_page_product-reviews') {
            return $clauses;
        }

        // Mevcut kullanıcı ID'sini al
        $current_user_id = get_current_user_id();

        // Kullanıcının ürünlerini al
        $user_products = $this->get_user_product_ids($current_user_id);

        if (empty($user_products)) {
            // Kullanıcının hiç ürünü yoksa hiçbir değerlendirme gösterme
            $clauses['where'] .= " AND {$wpdb->comments}.comment_post_ID IN (0)";
            return $clauses;
        }

        // Sadece kullanıcının ürünlerine ait değerlendirmeleri göster
        $product_ids_string = implode(',', array_map('intval', $user_products));
        $clauses['where'] .= " AND {$wpdb->comments}.comment_post_ID IN ({$product_ids_string})";

        return $clauses;
    }



    /**
     * Tutor LMS öğrenciler tablosu için JavaScript filtreleme ekle
     * Sadece belirtilen tablo seçicisinde filtreleme yapar
     */
    public function add_tutor_students_table_filtering_js() {
        // Sadece tutor instructor rolündeki kullanıcılar için
        if (!$this->is_current_user_tutor_instructor()) {
            return;
        }

        // Sadece Tutor LMS öğrenciler sayfasında çalışsın
        if (!isset($_GET['page']) || $_GET['page'] !== 'tutor-students') {
            return;
        }

        $current_user_id = get_current_user_id();

        // Kullanıcının kurslarını al
        $user_courses = get_posts([
            'post_type' => 'courses',
            'author' => $current_user_id,
            'posts_per_page' => -1,
            'fields' => 'ids',
            'post_status' => ['publish', 'private', 'draft', 'pending']
        ]);

        if (empty($user_courses)) {
            // Kullanıcının kursu yoksa tabloyu gizle
            ?>
            <script type="text/javascript">
            jQuery(document).ready(function($) {
                // Belirtilen tablo seçicisini gizle
                $('#wpbody-content > div.tutor-admin-wrap > div.tutor-admin-body > div.tutor-table-responsive.tutor-mt-24 > table').closest('.tutor-table-responsive').hide();

                // Boş durum mesajı göster
                $('#wpbody-content > div.tutor-admin-wrap > div.tutor-admin-body').append('<div class="tutor-empty-state"><p>Henüz hiç kursa sahip değilsiniz.</p></div>');
            });
            </script>
            <?php
            return;
        }

        // Kullanıcının kurslarına kayıtlı öğrencileri al - alternatif yöntem
        global $wpdb;
        $enrolled_students = [];

        if (!empty($user_courses)) {
            $course_ids = implode(',', array_map('intval', $user_courses));
            $students_query = $wpdb->get_results($wpdb->prepare("
                SELECT DISTINCT posts.post_author as student_id
                FROM {$wpdb->posts} AS posts
                WHERE posts.post_type = %s
                AND posts.post_status = %s
                AND posts.post_parent IN ({$course_ids})
            ", 'tutor_enrolled', 'completed'));

            foreach ($students_query as $student) {
                $enrolled_students[] = intval($student->student_id);
            }
        }

        ?>
        <script type="text/javascript">
        jQuery(document).ready(function($) {
            console.log('Role Custom: Tutor Students Filtering Started');

            // Kullanıcının öğrencilerinin ID'leri
            var enrolledStudents = <?php echo json_encode($enrolled_students); ?>;
            console.log('Role Custom: Enrolled Students:', enrolledStudents);

            // Belirtilen tablo seçicisini kullan - daha esnek seçici
            var targetTable = $('.tutor-table-responsive.tutor-mt-24 table');
            console.log('Role Custom: Target Table Found:', targetTable.length > 0);

            // Eğer bulamazsa alternatif seçici dene
            if (targetTable.length === 0) {
                targetTable = $('.tutor-table-responsive table');
                console.log('Role Custom: Alternative Table Found:', targetTable.length > 0);
            }

            if (targetTable.length > 0) {
                var totalRows = 0;
                var hiddenRows = 0;

                // Tablo satırlarını kontrol et
                targetTable.find('tbody tr').each(function() {
                    totalRows++;
                    var row = $(this);
                    var checkbox = row.find('input[type="checkbox"].tutor-bulk-checkbox');

                    if (checkbox.length > 0) {
                        var studentId = parseInt(checkbox.val());
                        console.log('Role Custom: Checking Student ID:', studentId);

                        // Eğer bu öğrenci kullanıcının kurslarına kayıtlı değilse, satırı gizle
                        if (studentId > 0 && enrolledStudents.indexOf(studentId) === -1) {
                            row.hide();
                            hiddenRows++;
                            console.log('Role Custom: Hidden Student ID:', studentId);
                        }
                    }
                });

                console.log('Role Custom: Total Rows:', totalRows, 'Hidden Rows:', hiddenRows);

                // Eğer hiç görünür satır kalmadıysa, boş durum mesajı göster
                var visibleRows = targetTable.find('tbody tr:visible');
                if (visibleRows.length === 0) {
                    targetTable.closest('.tutor-table-responsive').after('<div class="tutor-empty-state"><p>Bu kurslara kayıtlı öğrenci bulunmamaktadır.</p></div>');
                }
            }
        });
        </script>
        <?php
    }

    /**
     * Tutor LMS öğrenciler sayfası için filtreleme kurulumu
     */
    public function setup_tutor_students_filtering() {
        $screen = get_current_screen();

        // Tutor LMS öğrenciler sayfası kontrolü
        if ($screen && $screen->id === 'tutor-lms_page_tutor-students') {
            // Sadece tutor instructor rolündeki kullanıcılar için
            if ($this->is_current_user_tutor_instructor()) {
                // Bu sayfada özel filtreleme aktif olacak
                $this->tutor_students_filtering_active = true;

                // Kullanıcının kurslarını al
                $current_user_id = get_current_user_id();
                $user_courses = get_posts([
                    'post_type' => 'courses',
                    'author' => $current_user_id,
                    'posts_per_page' => -1,
                    'fields' => 'ids',
                    'post_status' => ['publish', 'private', 'draft', 'pending']
                ]);

                $this->user_courses_for_filtering = $user_courses;
            }
        }
    }

    /**
     * Tutor LMS kurs oluşturucu ekranında Author bölümünü gizle
     * Sadece tutor_instructor rolündeki kullanıcılar için
     */
    public function hide_tutor_course_author_section() {
        // Sadece tutor instructor rolündeki kullanıcılar için
        if (!$this->is_current_user_tutor_instructor()) {
            return;
        }

        // Sadece Tutor LMS kurs oluşturucu sayfasında
        $screen = get_current_screen();
        if (!$screen || $screen->id !== 'tutor-lms_page_create-course') {
            return;
        }

        // Categories, Tags, Author bölümleri ve Content Drip sekmesini gizlemek için CSS
        ?>
        <style type="text/css">
            /* Tutor LMS kurs oluşturucu ekranında Categories, Tags ve Author bölümlerini gizle */
            #tutor-course-builder > div.css-1fs22e1 > div.css-v6epif > div > div.css-ufdi3s > div:nth-child(7),
            #tutor-course-builder > div.css-1fs22e1 > div.css-v6epif > div > div.css-ufdi3s > div:nth-child(8),
            #tutor-course-builder > div.css-1fs22e1 > div.css-v6epif > div > div.css-ufdi3s > div:nth-child(9) {
                display: none !important;
            }

            /* Tutor LMS kurs oluşturucu ekranında Content Drip sekmesini gizle */
            #tutor-course-builder > div.css-1fs22e1 > div.css-v6epif > div > div.css-1o0dlfz > div.css-1i43dhb > div:nth-child(3) > div > div.css-pw7jst > div > button.css-1iyecux {
                display: none !important;
            }
        </style>
        <?php
    }


}

// Eklentiyi başlat
function role_custom_init() {
    return Role_Custom::get_instance();
}

// WordPress yüklendikten sonra eklentiyi başlat
add_action('plugins_loaded', 'role_custom_init');

// Eklenti bilgilerini döndüren yardımcı fonksiyon
function role_custom() {
    return Role_Custom::get_instance();
}

// Eklenti etkinleştirme/devre dışı bırakma hook'ları
register_activation_hook(ROLE_CUSTOM_PLUGIN_FILE, 'role_custom_activate');
register_deactivation_hook(ROLE_CUSTOM_PLUGIN_FILE, 'role_custom_deactivate');

/**
 * Eklenti etkinleştirme fonksiyonu
 */
function role_custom_activate() {
    $role_custom = Role_Custom::get_instance();
    $role_custom->activate();
}

/**
 * Eklenti devre dışı bırakma fonksiyonu
 */
function role_custom_deactivate() {
    $role_custom = Role_Custom::get_instance();
    $role_custom->deactivate();
}

/**
 * Global fonksiyon: Eğitmen meta verilerini temizle
 * 17 Kurs istatistikleri eklentisindeki clean_instructor_meta_data() fonksiyonunun aynısı
 *
 * @param int $user_id Kullanıcı ID'si
 * @return bool İşlem başarılı oldu mu
 */
function role_custom_clean_instructor_meta_data($user_id) {
    $role_custom = Role_Custom::get_instance();
    $result = $role_custom->clean_instructor_meta_data($user_id);
    return $result['success'];
}

/**
 * Kullanıcı rolü değiştiğinde eğitmen meta verilerini temizleyen hook fonksiyonu
 * 17 Kurs istatistikleri eklentisindeki check_admin_bar_on_role_change() fonksiyonunun mantığı
 *
 * @param int $user_id Kullanıcı ID'si
 * @param string $role Yeni rol
 * @param array $old_roles Eski roller
 */
function role_custom_check_role_change_for_instructor_cleanup($user_id, $role, $old_roles) {
    // Admin rolü ise işlem yapma
    if ($role === 'administrator') {
        return;
    }

    // Eğer kullanıcı tutor_instructor rolünden başka bir role geçiyorsa meta verileri temizle
    if (in_array('tutor_instructor', $old_roles) && $role !== 'tutor_instructor') {
        role_custom_clean_instructor_meta_data($user_id);
        error_log("Role Custom: Kullanıcı (ID: {$user_id}) tutor_instructor'dan {$role}'e geçti, meta veriler temizlendi.");
    }
}

// Hook'u ekle - 17 Kurs istatistikleri eklentisindeki gibi
add_action('set_user_role', 'role_custom_check_role_change_for_instructor_cleanup', 10, 3);